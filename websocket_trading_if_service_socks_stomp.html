<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NextGen WebSocket Client (SockJS + STOMP)</title>
    <!-- SockJS and STOMP libraries -->
    <!--<script src="https://cdn.jsdelivr.net/npm/sockjs-client@1.6.1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/stompjs@2.3.3/lib/stomp.min.js"></script>-->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sockjs-client/1.6.1/sockjs.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/stomp.js/2.3.3/stomp.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .container {
            display: flex;
            gap: 20px;
        }
        .panel {
            flex: 1;
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .log-panel {
            height: 400px;
            overflow-y: auto;
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
        }
        .message {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }
        .sent {
            background-color: #e6f7ff;
        }
        .received {
            background-color: #f6ffed;
        }
        .error {
            background-color: #fff1f0;
        }
        button {
            padding: 8px 12px;
            margin: 5px;
            cursor: pointer;
        }
        input, select, textarea {
            padding: 8px;
            margin: 5px 0;
            width: 100%;
            box-sizing: border-box;
        }
        h2 {
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>NextGen WebSocket Client (SockJS + STOMP)</h1>

    <div class="panel">
        <h2>Connection</h2>
        <div class="form-group">
            <label for="serverUrl">Server URL:</label>
            <input type="text" id="serverUrl" value="http://localhost:8766/ws" />
        </div>
        <div class="form-group">
            <label for="jwtToken">JWT Token:</label>
            <input type="text" id="jwtToken" placeholder="Your JWT token" />
        </div>
        <button id="connectBtn">Connect</button>
        <button id="disconnectBtn" disabled>Disconnect</button>
    </div>

    <div class="container">
        <div class="panel">
            <h2>Subscriptions</h2>

            <div class="form-group">
                <label for="subscriptionType">Subscription Type:</label>
                <select id="subscriptionType">
                    <option value="quotes">Quotes</option>
                    <option value="ping">Ping</option>
                </select>
            </div>

            <div id="quotesParams" class="subscription-params">
                <div class="form-group">
                    <label for="quoteSymbol">Symbol:</label>
                    <input type="text" id="quoteSymbol" placeholder="e.g. BTC-EUR" value="BTC-EUR" />
                </div>
            </div>

            <button id="subscribeBtn" disabled>Subscribe</button>
            <button id="unsubscribeBtn" disabled>Unsubscribe</button>
        </div>

        <div class="panel">
            <h2>RFQ Operations</h2>

            <div class="form-group">
                <label for="rfqSymbol">Symbol:</label>
                <input type="text" id="rfqSymbol" placeholder="e.g. BTC-EUR" value="BTC-EUR" />
            </div>

            <div class="form-group">
                <label for="rfqCurrency">Currency:</label>
                <input type="text" id="rfqCurrency" placeholder="e.g. EUR" value="EUR" />
            </div>

            <div class="form-group">
                <label for="rfqSide">Side:</label>
                <select id="rfqSide">
                    <option value="Buy">Buy</option>
                    <option value="Sell">Sell</option>
                </select>
            </div>

            <div class="form-group">
                <label for="rfqQuantity">Quantity:</label>
                <input type="text" id="rfqQuantity" placeholder="e.g. 0.01" value="0.01" />
            </div>

            <div class="form-group">
                <label for="rfqId">RFQ ID (for cancel/order):</label>
                <input type="text" id="rfqId" placeholder="RFQ ID from response" />
            </div>

            <button id="submitRfqBtn" disabled>Submit RFQ</button>
            <button id="cancelRfqBtn" disabled>Cancel RFQ</button>
            <button id="submitOrderBtn" disabled>Submit Order</button>
        </div>
    </div>

    <div class="panel">
        <h2>Custom Message</h2>
        <div class="form-group">
            <label for="customCommand">Command:</label>
            <input type="text" id="customCommand" placeholder="e.g. ping" />
        </div>
        <div class="form-group">
            <label for="customData">JSON Data:</label>
            <textarea id="customData" rows="6" placeholder='{"key": "value"}'></textarea>
        </div>
        <button id="sendCustomBtn" disabled>Send Custom Message</button>
    </div>

    <div class="panel">
        <h2>Message Log</h2>
        <button id="clearLogBtn">Clear Log</button>
        <div id="messageLog" class="log-panel"></div>
    </div>

    <script>
        // Global variables
        let stompClient = null;
        let requestCounter = 1;
        let activeSubscriptions = {};

        // DOM elements
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const subscribeBtn = document.getElementById('subscribeBtn');
        const unsubscribeBtn = document.getElementById('unsubscribeBtn');
        const submitRfqBtn = document.getElementById('submitRfqBtn');
        const cancelRfqBtn = document.getElementById('cancelRfqBtn');
        const submitOrderBtn = document.getElementById('submitOrderBtn');
        const sendCustomBtn = document.getElementById('sendCustomBtn');
        const clearLogBtn = document.getElementById('clearLogBtn');
        const messageLog = document.getElementById('messageLog');
        const subscriptionType = document.getElementById('subscriptionType');

        // Show/hide subscription params based on selection
        subscriptionType.addEventListener('change', function() {
            document.querySelectorAll('.subscription-params').forEach(el => el.style.display = 'none');
            const selectedType = this.value;
            const paramsDiv = document.getElementById(`${selectedType}Params`);
            if (paramsDiv) {
                paramsDiv.style.display = 'block';
            }
        });

        // Connect to WebSocket
        connectBtn.addEventListener('click', function() {
            const serverUrl = document.getElementById('serverUrl').value;
            const jwtToken = document.getElementById('jwtToken').value;

            try {
                // Create SockJS instance
                const socket = new SockJS(serverUrl);

                // Create STOMP client over SockJS
                stompClient = Stomp.over(socket);

                // Disable debug logging
                stompClient.debug = null;

                // Connect to the server
                /*const headers = {};
                if (jwtToken) {
                    headers['Authorization'] = `Bearer ${jwtToken}`;
                    headers['Access-Control-Allow-Origin'] = `*`;
                }*/

                const headers = {
                    'Authorization': `Bearer ${jwtToken}`,
                    'Origin': `*`
                };

                stompClient.connect(headers, function(frame) {
                    logMessage('Connected to STOMP: ' + frame, 'system');

                    // Enable buttons
                    connectBtn.disabled = true;
                    disconnectBtn.disabled = false;
                    subscribeBtn.disabled = false;
                    unsubscribeBtn.disabled = false;
                    submitRfqBtn.disabled = false;
                    cancelRfqBtn.disabled = false;
                    submitOrderBtn.disabled = false;
                    sendCustomBtn.disabled = false;

                    // Subscribe to system messages
                    stompClient.subscribe('/user/queue/messages', function(message) {
                        const body = JSON.parse(message.body);
                        logMessage('Received system message: ' + message.body, 'received');
                    });

                }, function(error) {
                    logMessage('STOMP Error: ' + error, 'error');
                    resetConnectionState();
                });

            } catch (error) {
                logMessage('Connection Error: ' + error.message, 'error');
                resetConnectionState();
            }
        });

        // Disconnect from WebSocket
        disconnectBtn.addEventListener('click', function() {
            if (stompClient) {
                stompClient.disconnect(function() {
                    logMessage('Disconnected from STOMP server', 'system');
                    resetConnectionState();
                });
            }
        });

        // Subscribe to a stream
        subscribeBtn.addEventListener('click', function() {
            if (!stompClient || !stompClient.connected) {
                logMessage('STOMP is not connected', 'error');
                return;
            }

            const type = subscriptionType.value;
            let destination = '';
            let message = {
                command: '',
                reqid: requestCounter++,
                data: {}
            };

            switch (type) {
                case 'quotes':
                    const symbol = document.getElementById('quoteSymbol').value;
                    if (!symbol) {
                        logMessage('Symbol is required for quote subscription', 'error');
                        return;
                    }
                    destination = '/app/subscribe_quotes';
                    message.command = 'subscribe_quotes';
                    message.data = { symbol: symbol };

                    // Subscribe to quote updates
                    const subId = stompClient.subscribe(`/topic/quotes/${symbol}`, function(message) {
                        const body = JSON.parse(message.body);
                        logMessage('Received quote: ' + message.body, 'received');
                    });

                    activeSubscriptions[symbol] = subId;
                    break;

                case 'ping':
                    destination = '/app/ping';
                    message.command = 'ping';
                    break;

                default:
                    logMessage(`Subscription type ${type} not implemented`, 'error');
                    return;
            }

            stompClient.send(destination, {}, JSON.stringify(message));
            logMessage('Sent: ' + JSON.stringify(message), 'sent');
        });

        // Unsubscribe from a stream
        unsubscribeBtn.addEventListener('click', function() {
            if (!stompClient || !stompClient.connected) {
                logMessage('STOMP is not connected', 'error');
                return;
            }

            const type = subscriptionType.value;
            let destination = '';
            let message = {
                command: '',
                reqid: requestCounter++,
                data: {}
            };

            switch (type) {
                case 'quotes':
                    const symbol = document.getElementById('quoteSymbol').value;
                    if (!symbol) {
                        logMessage('Symbol is required for quote unsubscription', 'error');
                        return;
                    }

                    destination = '/app/unsubscribe_quotes';
                    message.command = 'unsubscribe_quotes';
                    message.data = { symbol: symbol };

                    // Unsubscribe from topic
                    if (activeSubscriptions[symbol]) {
                        activeSubscriptions[symbol].unsubscribe();
                        delete activeSubscriptions[symbol];
                    }
                    break;

                default:
                    logMessage(`Unsubscribe not implemented for ${type}`, 'error');
                    return;
            }

            stompClient.send(destination, {}, JSON.stringify(message));
            logMessage('Sent: ' + JSON.stringify(message), 'sent');
        });

        // Submit RFQ
        submitRfqBtn.addEventListener('click', function() {
            if (!stompClient || !stompClient.connected) {
                logMessage('STOMP is not connected', 'error');
                return;
            }

            const symbol = document.getElementById('rfqSymbol').value;
            const currency = document.getElementById('rfqCurrency').value;
            const side = document.getElementById('rfqSide').value;
            const quantity = document.getElementById('rfqQuantity').value;

            if (!symbol || !quantity) {
                logMessage('Symbol and quantity are required for RFQ', 'error');
                return;
            }

            const message = {
                command: 'submit_rfq',
                reqid: requestCounter++,
                data: {
                    symbol: symbol,
                    currency: currency,
                    order_qty: quantity,
                    side: side,
                    quote_req_id: `client-rfq-${Date.now()}`
                }
            };

            stompClient.send('/app/submit_rfq', {}, JSON.stringify(message));
            logMessage('Sent: ' + JSON.stringify(message), 'sent');
        });

        // Cancel RFQ
        cancelRfqBtn.addEventListener('click', function() {
            if (!stompClient || !stompClient.connected) {
                logMessage('STOMP is not connected', 'error');
                return;
            }

            const rfqId = document.getElementById('rfqId').value;
            if (!rfqId) {
                logMessage('RFQ ID is required to cancel an RFQ', 'error');
                return;
            }

            const message = {
                command: 'cancel_rfq',
                reqid: requestCounter++,
                data: {
                    rfq_id: rfqId
                }
            };

            stompClient.send('/app/cancel_rfq', {}, JSON.stringify(message));
            logMessage('Sent: ' + JSON.stringify(message), 'sent');
        });

        // Submit Order
        submitOrderBtn.addEventListener('click', function() {
            if (!stompClient || !stompClient.connected) {
                logMessage('STOMP is not connected', 'error');
                return;
            }

            const rfqId = document.getElementById('rfqId').value;
            if (!rfqId) {
                logMessage('RFQ ID is required to submit an order', 'error');
                return;
            }

            const message = {
                command: 'submit_order',
                reqid: requestCounter++,
                data: {
                    RFQID: rfqId
                }
            };

            stompClient.send('/app/submit_order', {}, JSON.stringify(message));
            logMessage('Sent: ' + JSON.stringify(message), 'sent');
        });

        // Send custom message
        sendCustomBtn.addEventListener('click', function() {
            if (!stompClient || !stompClient.connected) {
                logMessage('STOMP is not connected', 'error');
                return;
            }

            const command = document.getElementById('customCommand').value;
            if (!command) {
                logMessage('Command is required for custom message', 'error');
                return;
            }

            let data = {};
            try {
                const dataText = document.getElementById('customData').value;
                if (dataText) {
                    data = JSON.parse(dataText);
                }
            } catch (error) {
                logMessage('Invalid JSON data: ' + error.message, 'error');
                return;
            }

            const message = {
                command: command,
                reqid: requestCounter++,
                data: data
            };

            stompClient.send(`/app/${command}`, {}, JSON.stringify(message));
            logMessage('Sent: ' + JSON.stringify(message), 'sent');
        });

        // Clear log
        clearLogBtn.addEventListener('click', function() {
            messageLog.innerHTML = '';
        });

        // Helper functions
        function logMessage(message, type) {
            const msgDiv = document.createElement('div');
            msgDiv.className = `message ${type}`;
            msgDiv.textContent = new Date().toLocaleTimeString() + ' - ' + message;
            messageLog.appendChild(msgDiv);
            messageLog.scrollTop = messageLog.scrollHeight;
        }

        function resetConnectionState() {
            stompClient = null;
            connectBtn.disabled = false;
            disconnectBtn.disabled = true;
            subscribeBtn.disabled = true;
            unsubscribeBtn.disabled = true;
            submitRfqBtn.disabled = true;
            cancelRfqBtn.disabled = true;
            submitOrderBtn.disabled = true;
            sendCustomBtn.disabled = true;
            activeSubscriptions = {};
        }
    </script>
</body>
</html>