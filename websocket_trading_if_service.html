<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NextGen WebSocket Client</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .container {
            display: flex;
            gap: 20px;
        }
        .panel {
            flex: 1;
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .log-panel {
            height: 400px;
            overflow-y: auto;
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
        }
        .message {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }
        .sent {
            background-color: #e6f7ff;
        }
        .received {
            background-color: #f6ffed;
        }
        .error {
            background-color: #fff1f0;
        }
        button {
            padding: 8px 12px;
            margin: 5px;
            cursor: pointer;
        }
        input, select, textarea {
            padding: 8px;
            margin: 5px 0;
            width: 100%;
            box-sizing: border-box;
        }
        h2 {
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>NextGen WebSocket Client</h1>
    
    <div class="panel">
        <h2>Connection</h2>
        <div class="form-group">
            <label for="wsUrl">WebSocket URL:</label>
            <input type="text" id="wsUrl" value="ws://localhost:8766" />
        </div>
        <button id="connectBtn">Connect</button>
        <button id="disconnectBtn" disabled>Disconnect</button>
    </div>
    
    <div class="container">
        <div class="panel">
            <h2>Subscriptions</h2>
            
            <div class="form-group">
                <label for="subscriptionType">Subscription Type:</label>
                <select id="subscriptionType">
                    <option value="quotes">Quotes</option>
                    <option value="trades">Trades</option>
                    <option value="balances">Balances</option>
                    <option value="exposures">Exposures</option>
                    <option value="securities">Securities</option>
                    <option value="currencies">Currencies</option>
                </select>
            </div>
            
            <div id="quotesParams" class="subscription-params">
                <div class="form-group">
                    <label for="quoteSymbol">Symbol:</label>
                    <input type="text" id="quoteSymbol" placeholder="e.g. BTC-EUR" />
                </div>
            </div>
            
            <div id="tradesParams" class="subscription-params" style="display:none;">
                <div class="form-group">
                    <label for="tradeStartDate">Start Date:</label>
                    <input type="datetime-local" id="tradeStartDate" />
                </div>
            </div>
            
            <div id="balancesParams" class="subscription-params" style="display:none;">
                <div class="form-group">
                    <label for="equivalentCurrency">Equivalent Currency:</label>
                    <input type="text" id="equivalentCurrency" value="USD" />
                </div>
            </div>
            
            <div id="securitiesParams" class="subscription-params" style="display:none;">
                <div class="form-group">
                    <label for="securitySymbols">Symbols (comma separated):</label>
                    <input type="text" id="securitySymbols" placeholder="e.g. BTC-USD,ETH-USD" />
                </div>
            </div>
            
            <button id="subscribeBtn" disabled>Subscribe</button>
            <button id="unsubscribeBtn" disabled>Unsubscribe</button>
        </div>
        
        <div class="panel">
            <h2>RFQ Operations</h2>

            <div class="form-group">
                <label for="rfqSymbol">Symbol:</label>
                <input type="text" id="rfqSymbol" placeholder="e.g. BTC-EUR" />
            </div>

            <div class="form-group">
                <label for="rfqCurrency">Currency:</label>
                <input type="text" id="rfqCurrency" placeholder="e.g. EUR" />
            </div>
            
            <div class="form-group">
                <label for="rfqSide">Side:</label>
                <select id="rfqSide">
                    <option value="Buy">Buy</option>
                    <option value="Sell">Sell</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="rfqQuantity">Quantity:</label>
                <input type="number" id="rfqQuantity" placeholder="e.g. 1.5" step="0.0001" />
            </div>

            <div class="form-group">
                <label for="rfqQuoteReqId">QuoteReqId:</label>
                <input type="text" id="rfqQuoteReqId" placeholder="e.g. cccbb633-690c-4194-9e8b-29f48499e4a9" />
            </div>
            
            <button id="submitRfqBtn" disabled>Submit RFQ</button>
            <button id="cancelRfqBtn" disabled>Cancel RFQ</button>
        </div>
    </div>
    
    <div class="panel">
        <h2>Custom Message</h2>
        <div class="form-group">
            <label for="customMessage">JSON Message:</label>
            <textarea id="customMessage" rows="6" placeholder='{"command": "example", "reqid": 1, "data": {}}'></textarea>
        </div>
        <button id="sendCustomBtn" disabled>Send Custom Message</button>
    </div>
    
    <div class="panel">
        <h2>Message Log</h2>
        <button id="clearLogBtn">Clear Log</button>
        <div id="messageLog" class="log-panel"></div>
    </div>
    
    <script>
        let socket = null;
        let requestCounter = 1;
        let activeSubscriptions = {};
        
        // DOM elements
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const subscribeBtn = document.getElementById('subscribeBtn');
        const unsubscribeBtn = document.getElementById('unsubscribeBtn');
        const submitRfqBtn = document.getElementById('submitRfqBtn');
        const cancelRfqBtn = document.getElementById('cancelRfqBtn');
        const sendCustomBtn = document.getElementById('sendCustomBtn');
        const clearLogBtn = document.getElementById('clearLogBtn');
        const messageLog = document.getElementById('messageLog');
        const subscriptionType = document.getElementById('subscriptionType');
        
        // Show/hide subscription params based on selection
        subscriptionType.addEventListener('change', function() {
            document.querySelectorAll('.subscription-params').forEach(el => el.style.display = 'none');
            const selectedType = this.value;
            const paramsDiv = document.getElementById(`${selectedType}Params`);
            if (paramsDiv) {
                paramsDiv.style.display = 'block';
            }
        });
        
        // Connect to WebSocket
        connectBtn.addEventListener('click', function() {
            const wsUrl = document.getElementById('wsUrl').value;
            
            try {
                console.log('wsUrl:', wsUrl);
                const jwtToken = 'Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICI3NlFJMGFsTFF4SDFZVkdYODB4SGNBc0hOcVZDaXpEWUFwbTdVUHlEN3JBIn0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.KPr7w_9MTZBEIP6sW80nf2f-rxDkQH8R-ID72OBx9q4pYrrURzvDnviG2Nje1m_CfkRLuSW_V1u37V4G2e-loTe4fd_PCU-V0EnVDTQtAvmU50a1Wri7WuRAtGKAL7DA8hDNiFoNhKvIfnDe9jPrNfOXv9m8m2k041wPcbyaKPkIcFlDXtfuV7hKR20EJ_eoUvBNJzzmsXc2-sSszqH-EaxzMGBKejURvbfErTi1JPsz61E-JFvC8BFL6Z_PIVK4BS3Lin3fRHMq8h9QK2kZlrv5t-bMrT8AZEugqfiZwO0OEdFR36P4qRINaiVY_CKGoJ6knqAgNsOyTuJcq6lg7g';
                // socket = new WebSocket(wsUrl + '?Authorization=Bearer%20' + jwtToken, ['authorization', jwtToken]);
                socket = new WebSocket('ws://localhost:8766?Authorization=' + jwtToken);

                socket.onopen = function() {
                    logMessage('Connected to WebSocket server', 'system');
                    connectBtn.disabled = true;
                    disconnectBtn.disabled = false;
                    subscribeBtn.disabled = false;
                    unsubscribeBtn.disabled = false;
                    submitRfqBtn.disabled = false;
                    cancelRfqBtn.disabled = false;
                    sendCustomBtn.disabled = false;
                };
                
                socket.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    logMessage('Received: ' + event.data, 'received');
                    
                    // Handle specific message types if needed
                    if (data.type === 'quote') {
                        // Handle quote updates
                    } else if (data.type === 'trade') {
                        // Handle trade updates
                    }
                };
                
                socket.onclose = function() {
                    logMessage('Disconnected from WebSocket server', 'system');
                    resetConnectionState();
                };
                
                socket.onerror = function(error) {
                    logMessage('WebSocket Error: ' + error.message, 'error');
                    resetConnectionState();
                };
            } catch (error) {
                console.error('Connection error:', error);
                logMessage('Connection Error: ' + error.message, 'error');
            }
        });
        
        // Disconnect from WebSocket
        disconnectBtn.addEventListener('click', function() {
            if (socket) {
                socket.close();
                resetConnectionState();
            }
        });
        
        // Subscribe to a stream
        subscribeBtn.addEventListener('click', function() {
            if (!socket || socket.readyState !== WebSocket.OPEN) {
                logMessage('WebSocket is not connected', 'error');
                return;
            }
            
            const type = subscriptionType.value;
            let request = {
                command: '',
                reqid: requestCounter++,
                data: {}
            };
            
            switch (type) {
                case 'quotes':
                    const symbol = document.getElementById('quoteSymbol').value;
                    if (!symbol) {
                        logMessage('Symbol is required for quote subscription', 'error');
                        return;
                    }
                    request.command = 'subscribe_quotes';
                    request.data = { symbol: symbol };
                    activeSubscriptions[symbol] = type;
                    break;
                    
                case 'trades':
                    const startDate = document.getElementById('tradeStartDate').value;
                    request.command = 'subscribe_trades';
                    if (startDate) {
                        request.data = { 
                            startDate: new Date(startDate).toISOString()
                        };
                    }
                    break;
                    
                case 'balances':
                    const currency = document.getElementById('equivalentCurrency').value;
                    request.command = 'subscribe_balances';
                    if (currency) {
                        request.data = { equivalentCurrency: currency };
                    }
                    break;
                    
                case 'exposures':
                    request.command = 'subscribe_exposures';
                    break;
                    
                case 'securities':
                    const symbols = document.getElementById('securitySymbols').value;
                    request.command = 'subscribe_securities';
                    if (symbols) {
                        request.data = { 
                            symbols: symbols.split(',').map(s => s.trim())
                        };
                    }
                    break;
                    
                case 'currencies':
                    request.command = 'subscribe_currencies';
                    break;
            }
            
            socket.send(JSON.stringify(request));
            logMessage('Sent: ' + JSON.stringify(request), 'sent');
        });
        
        // Unsubscribe from a stream
        unsubscribeBtn.addEventListener('click', function() {
            if (!socket || socket.readyState !== WebSocket.OPEN) {
                logMessage('WebSocket is not connected', 'error');
                return;
            }
            
            const type = subscriptionType.value;
            let request = {
                command: '',
                reqid: requestCounter++,
                data: {}
            };
            
            switch (type) {
                case 'quotes':
                    const symbol = document.getElementById('quoteSymbol').value;
                    if (!symbol) {
                        logMessage('Symbol is required for quote unsubscription', 'error');
                        return;
                    }
                    request.command = 'unsubscribe_quotes';
                    request.data = { symbol: symbol };
                    delete activeSubscriptions[symbol];
                    break;
                    
                // Add other unsubscribe types as needed
                default:
                    logMessage(`Unsubscribe not implemented for ${type}`, 'error');
                    return;
            }
            
            socket.send(JSON.stringify(request));
            logMessage('Sent: ' + JSON.stringify(request), 'sent');
        });
        
        // Submit RFQ
        submitRfqBtn.addEventListener('click', function() {
            if (!socket || socket.readyState !== WebSocket.OPEN) {
                logMessage('WebSocket is not connected', 'error');
                return;
            }
            
            const symbol = document.getElementById('rfqSymbol').value;
            const currency = document.getElementById('rfqCurrency').value;
            const quantity = document.getElementById('rfqQuantity').value;
            const side = document.getElementById('rfqSide').value;
            const quote_req_id = document.getElementById('rfqQuoteReqId').value;

            if (!symbol || !quantity) {
                logMessage('Symbol and quantity are required for RFQ', 'error');
                return;
            }
            
            const request = {
                command: 'submit_rfq',
                reqid: requestCounter++,
                data: {
                    symbol: symbol,
                    currency: currency,
                    order_qty: parseFloat(quantity),
                    side: side,
                    quote_req_id: quote_req_id
                }
            };
            
            socket.send(JSON.stringify(request));
            logMessage('Sent: ' + JSON.stringify(request), 'sent');
        });
        
        // Cancel RFQ
        cancelRfqBtn.addEventListener('click', function() {
            if (!socket || socket.readyState !== WebSocket.OPEN) {
                logMessage('WebSocket is not connected', 'error');
                return;
            }
            
            const rfqId = prompt('Enter RFQ ID to cancel:');
            if (!rfqId) return;
            
            const request = {
                command: 'cancel_rfq',
                reqid: requestCounter++,
                data: {
                    rfqId: rfqId
                }
            };
            
            socket.send(JSON.stringify(request));
            logMessage('Sent: ' + JSON.stringify(request), 'sent');
        });
        
        // Send custom message
        sendCustomBtn.addEventListener('click', function() {
            if (!socket || socket.readyState !== WebSocket.OPEN) {
                logMessage('WebSocket is not connected', 'error');
                return;
            }
            
            try {
                const message = JSON.parse(document.getElementById('customMessage').value);
                socket.send(JSON.stringify(message));
                logMessage('Sent: ' + JSON.stringify(message), 'sent');
            } catch (error) {
                logMessage('Invalid JSON: ' + error.message, 'error');
            }
        });
        
        // Clear log
        clearLogBtn.addEventListener('click', function() {
            messageLog.innerHTML = '';
        });
        
        // Helper functions
        function logMessage(message, type) {
            console.log('type: \'' + type + '\'; message: \'' + message + '\'');
            const msgDiv = document.createElement('div');
            msgDiv.className = `message ${type}`;
            msgDiv.textContent = message;
            messageLog.appendChild(msgDiv);
            messageLog.scrollTop = messageLog.scrollHeight;
        }
        
        function resetConnectionState() {
            socket = null;
            connectBtn.disabled = false;
            disconnectBtn.disabled = true;
            subscribeBtn.disabled = true;
            unsubscribeBtn.disabled = true;
            submitRfqBtn.disabled = true;
            cancelRfqBtn.disabled = true;
            sendCustomBtn.disabled = true;
            activeSubscriptions = {};
        }
    </script>
</body>
</html>