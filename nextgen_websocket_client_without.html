<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NextGen WebSocket Client</title>
    <!-- No SockJS or STOMP libraries needed -->
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2ecc71;
            --error-color: #e74c3c;
            --warning-color: #f39c12;
            --dark-color: #34495e;
            --light-color: #ecf0f1;
            --border-color: #bdc3c7;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            padding: 20px;
        }

        h1, h2, h3 {
            color: var(--dark-color);
            margin-bottom: 15px;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #ccc;
            margin-right: 5px;
        }

        .status-indicator.connected {
            background-color: var(--secondary-color);
        }

        .status-indicator.disconnected {
            background-color: var(--error-color);
        }

        .status-text {
            font-size: 14px;
            font-weight: bold;
        }

        .container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }

        .panel {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
            flex: 1;
            min-width: 300px;
        }

        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }

        .log-panel {
            height: 500px;
            overflow-y: auto;
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
        }

        .message {
            margin-bottom: 8px;
            padding: 8px;
            border-radius: 4px;
            word-break: break-word;
        }

        .message-time {
            font-weight: bold;
            margin-right: 8px;
        }

        .sent {
            background-color: #e3f2fd;
            border-left: 4px solid var(--primary-color);
        }

        .received {
            background-color: #e8f5e9;
            border-left: 4px solid var(--secondary-color);
        }

        .error {
            background-color: #ffebee;
            border-left: 4px solid var(--error-color);
        }

        .system {
            background-color: #fff8e1;
            border-left: 4px solid var(--warning-color);
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: var(--dark-color);
        }

        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: background-color 0.3s;
            margin-right: 5px;
            margin-bottom: 5px;
        }

        button:hover {
            background-color: #2980b9;
        }

        button:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }

        .btn-success {
            background-color: var(--secondary-color);
        }

        .btn-success:hover {
            background-color: #27ae60;
        }

        .btn-danger {
            background-color: var(--error-color);
        }

        .btn-danger:hover {
            background-color: #c0392b;
        }

        .btn-warning {
            background-color: var(--warning-color);
        }

        .btn-warning:hover {
            background-color: #d35400;
        }

        .tabs {
            display: flex;
            margin-bottom: 15px;
            border-bottom: 1px solid var(--border-color);
        }

        .tab {
            padding: 10px 15px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            margin-right: 5px;
        }

        .tab.active {
            border-bottom: 2px solid var(--primary-color);
            font-weight: bold;
            color: var(--primary-color);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .subscription-params {
            display: none;
        }

        .alert {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .alert-error {
            background-color: #ffebee;
            color: var(--error-color);
        }

        .alert-success {
            background-color: #e8f5e9;
            color: var(--secondary-color);
        }

        .alert-warning {
            background-color: #fff8e1;
            color: var(--warning-color);
        }

        .json-key {
            color: #f92672;
        }

        .json-value {
            color: #a6e22e;
        }

        .json-string {
            color: #e6db74;
        }

        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }

            .panel {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>NextGen WebSocket Client</h1>
        <div>
            <span class="status-indicator disconnected" id="connectionStatus"></span>
            <span class="status-text" id="connectionStatusText">Disconnected</span>
        </div>
    </div>

    <div class="panel">
        <div class="panel-header">
            <h2>Connection</h2>
        </div>
        <div class="form-group">
            <label for="serverUrl">Server URL:</label>
            <input type="text" id="serverUrl" value="ws://localhost:8766" />
        </div>
        <div class="form-group">
            <label for="jwtToken">JWT Token:</label>
            <input type="text" id="jwtToken" placeholder="Your JWT token" />
        </div>
        <div class="button-group">
            <button id="connectBtn">Connect</button>
            <button id="disconnectBtn" disabled>Disconnect</button>
            <button id="pingBtn" disabled>Ping</button>
        </div>
    </div>

    <div class="container">
        <div class="panel">
            <div class="panel-header">
                <h2>Operations</h2>
            </div>

            <div class="tabs">
                <div class="tab active" data-tab="subscriptions">Subscriptions</div>
                <div class="tab" data-tab="rfq">RFQ</div>
                <div class="tab" data-tab="orders">Orders</div>
                <div class="tab" data-tab="custom">Custom</div>
            </div>

            <!-- Subscriptions Tab -->
            <div class="tab-content active" id="subscriptions">
                <div class="form-group">
                    <label for="subscriptionType">Subscription Type:</label>
                    <select id="subscriptionType">
                        <option value="quotes">Quotes</option>
                        <option value="trades">Trades</option>
                        <option value="balances">Balances</option>
                        <option value="exposures">Exposures</option>
                        <option value="securities">Securities</option>
                        <option value="currencies">Currencies</option>
                    </select>
                </div>

                <div id="quotesParams" class="subscription-params" style="display:block;">
                    <div class="form-group">
                        <label for="quoteSymbol">Symbol:</label>
                        <input type="text" id="quoteSymbol" placeholder="e.g. BTC-EUR" value="BTC-EUR" />
                    </div>
                </div>

                <div id="tradesParams" class="subscription-params">
                    <div class="form-group">
                        <label for="tradeStartDate">Start Date (optional):</label>
                        <input type="datetime-local" id="tradeStartDate" />
                    </div>
                </div>

                <div id="balancesParams" class="subscription-params">
                    <div class="form-group">
                        <label for="equivalentCurrency">Equivalent Currency (optional):</label>
                        <input type="text" id="equivalentCurrency" value="USD" />
                    </div>
                </div>

                <div id="securitiesParams" class="subscription-params">
                    <div class="form-group">
                        <label for="securitySymbols">Symbols (comma separated, optional):</label>
                        <input type="text" id="securitySymbols" placeholder="e.g. BTC-USD,ETH-USD" />
                    </div>
                </div>

                <div class="button-group">
                    <button id="subscribeBtn" disabled>Subscribe</button>
                    <button id="unsubscribeBtn" disabled>Unsubscribe</button>
                </div>

                <div class="form-group" style="margin-top: 20px;">
                    <label>Active Subscriptions:</label>
                    <div id="activeSubscriptions" style="padding: 10px; background-color: #f5f5f5; border-radius: 4px; min-height: 50px;">
                        No active subscriptions
                    </div>
                </div>
            </div>

            <!-- RFQ Tab -->
            <div class="tab-content" id="rfq">
                <div class="form-group">
                    <label for="rfqSymbol">Symbol:</label>
                    <input type="text" id="rfqSymbol" placeholder="e.g. BTC-EUR" value="BTC-EUR" />
                </div>

                <div class="form-group">
                    <label for="rfqCurrency">Currency (optional):</label>
                    <input type="text" id="rfqCurrency" placeholder="e.g. EUR" />
                </div>

                <div class="form-group">
                    <label for="rfqSide">Side:</label>
                    <select id="rfqSide">
                        <option value="Buy">Buy</option>
                        <option value="Sell">Sell</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="rfqQuantity">Quantity:</label>
                    <input type="text" id="rfqQuantity" placeholder="e.g. 0.1" value="0.1" />
                </div>

                <div class="form-group">
                    <label for="rfqClientId">Client RFQ ID (optional):</label>
                    <input type="text" id="rfqClientId" placeholder="Custom client RFQ ID" />
                </div>

                <button id="submitRfqBtn" disabled>Submit RFQ</button>

                <div class="form-group" style="margin-top: 20px;">
                    <label for="cancelRfqId">RFQ ID to Cancel:</label>
                    <input type="text" id="cancelRfqId" placeholder="RFQ ID from response" />
                </div>

                <button id="cancelRfqBtn" disabled>Cancel RFQ</button>
            </div>

            <!-- Orders Tab -->
            <div class="tab-content" id="orders">
                <div class="form-group">
                    <label for="orderRfqId">RFQ ID:</label>
                    <input type="text" id="orderRfqId" placeholder="RFQ ID from response" />
                </div>

                <button id="submitOrderBtn" disabled>Submit Order</button>
            </div>

            <!-- Custom Tab -->
            <div class="tab-content" id="custom">
                <div class="form-group">
                    <label for="customCommand">Command:</label>
                    <input type="text" id="customCommand" placeholder="e.g. ping" />
                </div>
                <div class="form-group">
                    <label for="customData">JSON Data:</label>
                    <textarea id="customData" rows="6" placeholder='{"key": "value"}'></textarea>
                </div>
                <button id="sendCustomBtn" disabled>Send Custom Message</button>
            </div>
        </div>

        <div class="panel">
            <div class="panel-header">
                <h2>Message Log</h2>
                <button id="clearLogBtn" class="btn-warning">Clear Log</button>
            </div>
            <div id="messageLog" class="log-panel"></div>
        </div>
    </div>

    <script>
        // Global variables
        let socket = null;
        let requestCounter = 1;
        let activeSubscriptions = {};
        let connected = false;

        // DOM elements
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const pingBtn = document.getElementById('pingBtn');
        const subscribeBtn = document.getElementById('subscribeBtn');
        const unsubscribeBtn = document.getElementById('unsubscribeBtn');
        const submitRfqBtn = document.getElementById('submitRfqBtn');
        const cancelRfqBtn = document.getElementById('cancelRfqBtn');
        const submitOrderBtn = document.getElementById('submitOrderBtn');
        const sendCustomBtn = document.getElementById('sendCustomBtn');
        const clearLogBtn = document.getElementById('clearLogBtn');
        const messageLog = document.getElementById('messageLog');
        const subscriptionType = document.getElementById('subscriptionType');
        const connectionStatus = document.getElementById('connectionStatus');
        const connectionStatusText = document.getElementById('connectionStatusText');
        const activeSubscriptionsDiv = document.getElementById('activeSubscriptions');

        // Tab functionality
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', function() {
                // Remove active class from all tabs and contents
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

                // Add active class to clicked tab
                this.classList.add('active');

                // Show corresponding content
                const tabId = this.getAttribute('data-tab');
                document.getElementById(tabId).classList.add('active');
            });
        });

        // Show/hide subscription params based on selection
        subscriptionType.addEventListener('change', function() {
            document.querySelectorAll('.subscription-params').forEach(el => el.style.display = 'none');
            const selectedType = this.value;
            const paramsDiv = document.getElementById(`${selectedType}Params`);
            if (paramsDiv) {
                paramsDiv.style.display = 'block';
            }
        });

        // Connect to WebSocket
        connectBtn.addEventListener('click', function() {
            const serverUrl = document.getElementById('serverUrl').value;
            const jwtToken = document.getElementById('jwtToken').value;

            try {
                // Create WebSocket connection with JWT token in URL
                const wsUrl = serverUrl + (jwtToken ? `?Authorization=Bearer%20${encodeURIComponent(jwtToken)}` : '');
                socket = new WebSocket(wsUrl);

                logMessage('Connecting to WebSocket server...', 'system');

                // Connection opened
                socket.onopen = function(event) {
                    logMessage('Connected to WebSocket server', 'system');
                    updateConnectionStatus(true);

                    // Enable buttons
                    enableButtons(true);
                };

                // Listen for messages
                socket.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);

                        // Handle different message types
                        if (data.type === 'hello') {
                            logMessage(`Received welcome message: ${event.data}`, 'received');
                        } else if (data.type === 'quote') {
                            logMessage(`Received quote: ${event.data}`, 'received');
                        } else if (data.status === 'error') {
                            logMessage(`Received error: ${event.data}`, 'error');
                        } else {
                            logMessage(`Received: ${event.data}`, 'received');
                        }
                    } catch (error) {
                        logMessage(`Error parsing message: ${error.message}`, 'error');
                        logMessage(`Raw message: ${event.data}`, 'error');
                    }
                };

                // Connection closed
                socket.onclose = function(event) {
                    if (event.wasClean) {
                        logMessage(`Connection closed cleanly, code=${event.code}, reason=${event.reason}`, 'system');
                    } else {
                        logMessage('Connection died', 'error');
                    }
                    updateConnectionStatus(false);
                    resetConnectionState();
                };

                // Connection error
                socket.onerror = function(error) {
                    logMessage(`WebSocket error: ${error.message || 'Unknown error'}`, 'error');
                    updateConnectionStatus(false);
                };

            } catch (error) {
                logMessage(`Connection error: ${error.message}`, 'error');
                updateConnectionStatus(false);
                resetConnectionState();
            }
        });

        // Disconnect from WebSocket
        disconnectBtn.addEventListener('click', function() {
            if (socket) {
                socket.close(1000, "User disconnected");
                logMessage('Disconnected from WebSocket server', 'system');
                updateConnectionStatus(false);
                resetConnectionState();
            }
        });

        // Ping
        pingBtn.addEventListener('click', function() {
            if (!checkConnection()) return;

            const message = {
                command: 'ping',
                reqid: requestCounter++
            };

            sendMessage(message);
            logMessage(`Sent: ${JSON.stringify(message)}`, 'sent');
        });

        // Subscribe to a stream
        subscribeBtn.addEventListener('click', function() {
            if (!checkConnection()) return;

            const type = subscriptionType.value;
            let message = {
                command: '',
                reqid: requestCounter++,
                data: {}
            };

            switch (type) {
                case 'quotes':
                    const symbol = document.getElementById('quoteSymbol').value;
                    if (!symbol) {
                        logMessage('Symbol is required for quote subscription', 'error');
                        return;
                    }
                    message.command = 'subscribe_quotes';
                    message.data = { symbol: symbol };

                    // Track subscription locally
                    activeSubscriptions[`quotes_${symbol}`] = {
                        type: 'quotes',
                        params: { symbol: symbol }
                    };
                    break;

                case 'trades':
                    const startDate = document.getElementById('tradeStartDate').value;
                    message.command = 'subscribe_trades';
                    if (startDate) {
                        message.data = {
                            startDate: new Date(startDate).toISOString()
                        };
                    }

                    // Track subscription locally
                    activeSubscriptions['trades'] = {
                        type: 'trades',
                        params: startDate ? { startDate: startDate } : {}
                    };
                    break;

                case 'balances':
                    const equivalentCurrency = document.getElementById('equivalentCurrency').value;
                    message.command = 'subscribe_balances';
                    if (equivalentCurrency) {
                        message.data = { equivalentCurrency: equivalentCurrency };
                    }

                    // Track subscription locally
                    activeSubscriptions['balances'] = {
                        type: 'balances',
                        params: equivalentCurrency ? { equivalentCurrency: equivalentCurrency } : {}
                    };
                    break;

                case 'exposures':
                    message.command = 'subscribe_exposures';

                    // Track subscription locally
                    activeSubscriptions['exposures'] = {
                        type: 'exposures',
                        params: {}
                    };
                    break;

                case 'securities':
                    const securitySymbols = document.getElementById('securitySymbols').value;
                    message.command = 'subscribe_securities';
                    if (securitySymbols) {
                        message.data = { symbols: securitySymbols.split(',').map(s => s.trim()) };
                    }

                    // Track subscription locally
                    activeSubscriptions['securities'] = {
                        type: 'securities',
                        params: securitySymbols ? { symbols: securitySymbols } : {}
                    };
                    break;

                case 'currencies':
                    message.command = 'subscribe_currencies';

                    // Track subscription locally
                    activeSubscriptions['currencies'] = {
                        type: 'currencies',
                        params: {}
                    };
                    break;

                default:
                    logMessage(`Unknown subscription type: ${type}`, 'error');
                    return;
            }

            sendMessage(message);
            logMessage(`Sent: ${JSON.stringify(message)}`, 'sent');

            // Update active subscriptions display
            updateActiveSubscriptionsDisplay();
        });

        // Unsubscribe from a stream
        unsubscribeBtn.addEventListener('click', function() {
            if (!checkConnection()) return;

            const type = subscriptionType.value;
            let message = {
                command: '',
                reqid: requestCounter++,
                data: {}
            };
            let subscriptionKey = '';

            switch (type) {
                case 'quotes':
                    const symbol = document.getElementById('quoteSymbol').value;
                    if (!symbol) {
                        logMessage('Symbol is required for quote unsubscription', 'error');
                        return;
                    }
                    message.command = 'unsubscribe_quotes';
                    message.data = { symbol: symbol };
                    subscriptionKey = `quotes_${symbol}`;
                    break;

                case 'trades':
                    message.command = 'unsubscribe_trades';
                    subscriptionKey = 'trades';
                    break;

                case 'balances':
                    message.command = 'unsubscribe_balances';
                    subscriptionKey = 'balances';
                    break;

                case 'exposures':
                    message.command = 'unsubscribe_exposures';
                    subscriptionKey = 'exposures';
                    break;

                case 'securities':
                    message.command = 'unsubscribe_securities';
                    subscriptionKey = 'securities';
                    break;

                case 'currencies':
                    message.command = 'unsubscribe_currencies';
                    subscriptionKey = 'currencies';
                    break;

                default:
                    logMessage(`Unknown subscription type: ${type}`, 'error');
                    return;
            }

            // Check if subscription exists
            if (activeSubscriptions[subscriptionKey]) {
                // Remove from active subscriptions
                delete activeSubscriptions[subscriptionKey];

                // Send unsubscribe message to server
                sendMessage(message);
                logMessage(`Sent: ${JSON.stringify(message)}`, 'sent');

                // Update active subscriptions display
                updateActiveSubscriptionsDisplay();
            } else {
                logMessage(`No active subscription found for ${type}`, 'error');
            }
        });

        // Submit RFQ
        submitRfqBtn.addEventListener('click', function() {
            if (!checkConnection()) return;

            const symbol = document.getElementById('rfqSymbol').value;
            const currency = document.getElementById('rfqCurrency').value;
            const side = document.getElementById('rfqSide').value;
            const quantity = document.getElementById('rfqQuantity').value;
            const clientId = document.getElementById('rfqClientId').value || `client-rfq-${Date.now()}`;

            if (!symbol) {
                logMessage('Symbol is required for RFQ', 'error');
                return;
            }

            if (!quantity) {
                logMessage('Quantity is required for RFQ', 'error');
                return;
            }

            const message = {
                command: 'submit_rfq',
                reqid: requestCounter++,
                data: {
                    symbol: symbol,
                    side: side,
                    order_qty: quantity,
                    quote_req_id: clientId
                }
            };

            if (currency) {
                message.data.currency = currency;
            }

            sendMessage(message);
            logMessage(`Sent: ${JSON.stringify(message)}`, 'sent');
        });

        // Cancel RFQ
        cancelRfqBtn.addEventListener('click', function() {
            if (!checkConnection()) return;

            const rfqId = document.getElementById('cancelRfqId').value;

            if (!rfqId) {
                logMessage('RFQ ID is required to cancel an RFQ', 'error');
                return;
            }

            const message = {
                command: 'cancel_rfq',
                reqid: requestCounter++,
                data: {
                    rfq_id: rfqId
                }
            };

            sendMessage(message);
            logMessage(`Sent: ${JSON.stringify(message)}`, 'sent');
        });

        // Submit Order
        submitOrderBtn.addEventListener('click', function() {
            if (!checkConnection()) return;

            const rfqId = document.getElementById('orderRfqId').value;

            if (!rfqId) {
                logMessage('RFQ ID is required to submit an order', 'error');
                return;
            }

            const message = {
                command: 'submit_order',
                reqid: requestCounter++,
                data: {
                    RFQID: rfqId
                }
            };

            sendMessage(message);
            logMessage(`Sent: ${JSON.stringify(message)}`, 'sent');
        });

        // Send custom message
        sendCustomBtn.addEventListener('click', function() {
            if (!checkConnection()) return;

            const command = document.getElementById('customCommand').value;
            let data = {};

            if (!command) {
                logMessage('Command is required', 'error');
                return;
            }

            try {
                const dataText = document.getElementById('customData').value;
                if (dataText) {
                    data = JSON.parse(dataText);
                }
            } catch (error) {
                logMessage(`Invalid JSON data: ${error.message}`, 'error');
                return;
            }

            const message = {
                command: command,
                reqid: requestCounter++,
                data: data
            };

            sendMessage(message);
            logMessage(`Sent: ${JSON.stringify(message)}`, 'sent');
        });

        // Clear log
        clearLogBtn.addEventListener('click', function() {
            messageLog.innerHTML = '';
        });

        // Helper function to log messages
        function logMessage(message, type) {
            const messageElement = document.createElement('div');
            messageElement.className = `message ${type}`;

            const timeElement = document.createElement('span');
            timeElement.className = 'message-time';
            timeElement.textContent = new Date().toLocaleTimeString();

            messageElement.appendChild(timeElement);
            messageElement.appendChild(document.createTextNode(message));

            messageLog.appendChild(messageElement);
            messageLog.scrollTop = messageLog.scrollHeight;
        }

        // Helper function to send a message
        function sendMessage(message) {
            if (socket && socket.readyState === WebSocket.OPEN) {
                socket.send(JSON.stringify(message));
            } else {
                logMessage('WebSocket is not connected', 'error');
            }
        }

        // Helper function to update connection status
        function updateConnectionStatus(isConnected) {
            connected = isConnected;

            if (isConnected) {
                connectionStatus.classList.remove('disconnected');
                connectionStatus.classList.add('connected');
                connectionStatusText.textContent = 'Connected';
            } else {
                connectionStatus.classList.remove('connected');
                connectionStatus.classList.add('disconnected');
                connectionStatusText.textContent = 'Disconnected';
            }
        }

        // Helper function to enable/disable buttons
        function enableButtons(enabled) {
            disconnectBtn.disabled = !enabled;
            pingBtn.disabled = !enabled;
            subscribeBtn.disabled = !enabled;
            unsubscribeBtn.disabled = !enabled;
            submitRfqBtn.disabled = !enabled;
            cancelRfqBtn.disabled = !enabled;
            submitOrderBtn.disabled = !enabled;
            sendCustomBtn.disabled = !enabled;
            connectBtn.disabled = enabled;
        }

        // Helper function to reset connection state
        function resetConnectionState() {
            socket = null;
            activeSubscriptions = {};
            updateActiveSubscriptionsDisplay();
            enableButtons(false);
        }

        // Helper function to check if connected
        function checkConnection() {
            if (!socket || socket.readyState !== WebSocket.OPEN) {
                logMessage('Not connected to WebSocket server', 'error');
                updateConnectionStatus(false);
                return false;
            }
            return true;
        }

        // Helper function to update active subscriptions display
        function updateActiveSubscriptionsDisplay() {
            if (Object.keys(activeSubscriptions).length === 0) {
                activeSubscriptionsDiv.innerHTML = 'No active subscriptions';
                return;
            }

            let html = '';
            for (const [key, subscription] of Object.entries(activeSubscriptions)) {
                let paramsText = '';
                if (subscription.params) {
                    const paramEntries = Object.entries(subscription.params);
                    if (paramEntries.length > 0) {
                        paramsText = ' - ' + paramEntries.map(([k, v]) => `${k}: ${v}`).join(', ');
                    }
                }
                html += `<div class="subscription-item">${subscription.type}${paramsText}</div>`;
            }
            activeSubscriptionsDiv.innerHTML = html;
        }

        // Format JSON for display
        function formatJSON(json) {
            if (typeof json === 'string') {
                try {
                    json = JSON.parse(json);
                } catch (e) {
                    return json;
                }
            }

            return JSON.stringify(json, null, 2)
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g, function (match) {
                    let cls = 'json-value';
                    if (/^"/.test(match)) {
                        if (/:$/.test(match)) {
                            cls = 'json-key';
                        } else {
                            cls = 'json-string';
                        }
                    }
                    return '<span class="' + cls + '">' + match + '</span>';
                });
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Set default values
            const now = new Date();
            now.setMinutes(now.getMinutes() - 30); // 30 minutes ago

            const tradeStartDateInput = document.getElementById('tradeStartDate');
            if (tradeStartDateInput) {
                tradeStartDateInput.value = now.toISOString().slice(0, 16);
            }
        });
    </script>
</body>
</html>