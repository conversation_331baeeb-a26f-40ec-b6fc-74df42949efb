/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header styles */
header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

header h1 {
    font-size: 2rem;
    font-weight: 300;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 15px;
}

.connection-status span {
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 500;
    font-size: 0.9rem;
}

.status-connected {
    background-color: #4CAF50;
    color: white;
}

.status-disconnected {
    background-color: #f44336;
    color: white;
}

.status-connecting {
    background-color: #ff9800;
    color: white;
}

/* Button styles */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-warning {
    background-color: #ffc107;
    color: #212529;
}

.btn-info {
    background-color: #17a2b8;
    color: white;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

/* Navigation tabs */
.tabs {
    display: flex;
    background-color: white;
    border-radius: 10px 10px 0 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.tab-btn {
    flex: 1;
    padding: 15px 20px;
    background-color: #f8f9fa;
    border: none;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.tab-btn:hover {
    background-color: #e9ecef;
}

.tab-btn.active {
    background-color: white;
    border-bottom-color: #007bff;
    color: #007bff;
}

/* Tab content */
.tab-content {
    display: none;
    background-color: white;
    border-radius: 0 0 10px 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    min-height: 600px;
}

.tab-content.active {
    display: block;
}

/* Panel styles */
.panel {
    padding: 30px;
}

.panel h2 {
    color: #333;
    margin-bottom: 25px;
    font-weight: 400;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
}

.panel h3 {
    color: #555;
    margin: 25px 0 15px 0;
    font-weight: 500;
}

/* Form styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 5px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* Button groups */
.button-group {
    display: flex;
    gap: 10px;
    margin-top: 20px;
    flex-wrap: wrap;
}

/* Subscription controls */
.subscription-controls {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 25px;
}

.subscription-params {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #dee2e6;
}

/* Active subscriptions */
.active-subscriptions {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
}

.subscriptions-list {
    margin-top: 15px;
}

.subscription-item {
    background-color: white;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 10px;
    border-left: 4px solid #28a745;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.subscription-info {
    flex: 1;
}

.subscription-type {
    font-weight: 600;
    color: #333;
}

.subscription-params-text {
    color: #666;
    font-size: 0.85rem;
    margin-top: 5px;
}

/* Message controls */
.message-controls {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
}

.message-params {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #dee2e6;
}

/* API controls */
.api-controls {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
}

/* Message logs */
.log-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.message-logs {
    background-color: #1e1e1e;
    color: #f8f8f2;
    padding: 20px;
    border-radius: 8px;
    height: 500px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    line-height: 1.4;
}

.log-entry {
    margin-bottom: 10px;
    padding: 8px;
    border-radius: 4px;
    border-left: 4px solid transparent;
}

.log-entry.sent {
    background-color: rgba(0, 123, 255, 0.1);
    border-left-color: #007bff;
}

.log-entry.received {
    background-color: rgba(40, 167, 69, 0.1);
    border-left-color: #28a745;
}

.log-entry.error {
    background-color: rgba(220, 53, 69, 0.1);
    border-left-color: #dc3545;
}

.log-entry.info {
    background-color: rgba(23, 162, 184, 0.1);
    border-left-color: #17a2b8;
}

.log-timestamp {
    color: #6c757d;
    font-size: 0.8rem;
    margin-right: 10px;
}

.log-type {
    font-weight: bold;
    margin-right: 10px;
}

.log-content {
    white-space: pre-wrap;
    word-break: break-word;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .tabs {
        flex-direction: column;
    }
    
    .tab-btn {
        border-bottom: 1px solid #dee2e6;
        border-radius: 0;
    }
    
    .tab-btn.active {
        border-bottom-color: #007bff;
        border-left: 3px solid #007bff;
    }
    
    .panel {
        padding: 20px;
    }
    
    .button-group {
        flex-direction: column;
    }
    
    .connection-status {
        flex-direction: column;
        gap: 10px;
    }
}

/* Scrollbar styling for webkit browsers */
.message-logs::-webkit-scrollbar {
    width: 8px;
}

.message-logs::-webkit-scrollbar-track {
    background: #2d2d2d;
    border-radius: 4px;
}

.message-logs::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 4px;
}

.message-logs::-webkit-scrollbar-thumb:hover {
    background: #777;
}

/* Animation for status changes */
.status-connected,
.status-disconnected,
.status-connecting {
    animation: statusPulse 2s infinite;
}

@keyframes statusPulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}
