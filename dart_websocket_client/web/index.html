<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NextGen WebSocket Client</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>NextGen WebSocket Client</h1>
            <div class="connection-status">
                <span id="connection-status" class="status-disconnected">Disconnected</span>
                <button id="connect-btn" class="btn btn-primary">Connect</button>
                <button id="disconnect-btn" class="btn btn-secondary" disabled>Disconnect</button>
            </div>
        </header>

        <div class="main-content">
            <!-- Navigation Tabs -->
            <nav class="tabs">
                <button class="tab-btn active" data-tab="connection">Connection</button>
                <button class="tab-btn" data-tab="subscriptions">Subscriptions</button>
                <button class="tab-btn" data-tab="messages">Messages</button>
                <button class="tab-btn" data-tab="trading">Trading API</button>
                <button class="tab-btn" data-tab="withdrawals">Withdrawals API</button>
                <button class="tab-btn" data-tab="logs">Message Logs</button>
            </nav>

            <!-- Connection Tab -->
            <div id="connection-tab" class="tab-content active">
                <div class="panel">
                    <h2>Connection Settings</h2>
                    <div class="form-group">
                        <label for="ws-url">WebSocket URL:</label>
                        <input type="text" id="ws-url" value="ws://localhost:8766" placeholder="ws://localhost:8766">
                    </div>
                    <div class="form-group">
                        <label for="jwt-token">JWT Token:</label>
                        <textarea id="jwt-token" placeholder="Enter your JWT token here..." rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="api-base-url">API Base URL:</label>
                        <input type="text" id="api-base-url" value="http://localhost:8000" placeholder="http://localhost:8000">
                    </div>
                </div>
            </div>

            <!-- Subscriptions Tab -->
            <div id="subscriptions-tab" class="tab-content">
                <div class="panel">
                    <h2>Subscription Management</h2>
                    <div class="subscription-controls">
                        <div class="form-group">
                            <label for="subscription-type">Subscription Type:</label>
                            <select id="subscription-type">
                                <option value="quotes">Quotes</option>
                                <option value="trades">Trades</option>
                                <option value="balances">Balances</option>
                                <option value="exposures">Exposures</option>
                                <option value="securities">Securities</option>
                                <option value="currencies">Currencies</option>
                            </select>
                        </div>
                        
                        <!-- Quotes Parameters -->
                        <div id="quotes-params" class="subscription-params">
                            <div class="form-group">
                                <label for="quote-symbol">Symbol:</label>
                                <input type="text" id="quote-symbol" placeholder="e.g., BTC-EUR" value="BTC-EUR">
                            </div>
                        </div>

                        <!-- Securities Parameters -->
                        <div id="securities-params" class="subscription-params" style="display: none;">
                            <div class="form-group">
                                <label for="security-symbols">Symbols (comma-separated):</label>
                                <input type="text" id="security-symbols" placeholder="e.g., BTC-EUR,ETH-USD">
                            </div>
                        </div>

                        <div class="button-group">
                            <button id="subscribe-btn" class="btn btn-success">Subscribe</button>
                            <button id="unsubscribe-btn" class="btn btn-warning">Unsubscribe</button>
                        </div>
                    </div>

                    <div class="active-subscriptions">
                        <h3>Active Subscriptions</h3>
                        <div id="subscriptions-list" class="subscriptions-list">
                            <!-- Active subscriptions will be displayed here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Messages Tab -->
            <div id="messages-tab" class="tab-content">
                <div class="panel">
                    <h2>Send WebSocket Messages</h2>
                    <div class="message-controls">
                        <div class="form-group">
                            <label for="message-type">Message Type:</label>
                            <select id="message-type">
                                <option value="ping">Ping</option>
                                <option value="submit_rfq">Submit RFQ</option>
                                <option value="submit_order">Submit Order</option>
                                <option value="cancel_rfq">Cancel RFQ</option>
                                <option value="custom">Custom Message</option>
                            </select>
                        </div>

                        <!-- Ping Parameters -->
                        <div id="ping-params" class="message-params">
                            <p>No additional parameters required for ping.</p>
                        </div>

                        <!-- RFQ Parameters -->
                        <div id="rfq-params" class="message-params" style="display: none;">
                            <div class="form-group">
                                <label for="rfq-symbol">Symbol:</label>
                                <input type="text" id="rfq-symbol" placeholder="e.g., BTC-EUR" value="BTC-EUR">
                            </div>
                            <div class="form-group">
                                <label for="rfq-currency">Currency (optional):</label>
                                <input type="text" id="rfq-currency" placeholder="e.g., EUR">
                            </div>
                            <div class="form-group">
                                <label for="rfq-quantity">Order Quantity:</label>
                                <input type="text" id="rfq-quantity" placeholder="e.g., 0.01" value="0.01">
                            </div>
                            <div class="form-group">
                                <label for="rfq-side">Side:</label>
                                <select id="rfq-side">
                                    <option value="Buy">Buy</option>
                                    <option value="Sell">Sell</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="rfq-quote-req-id">Quote Request ID (optional):</label>
                                <input type="text" id="rfq-quote-req-id" placeholder="Client RFQ ID">
                            </div>
                        </div>

                        <!-- Order Parameters -->
                        <div id="order-params" class="message-params" style="display: none;">
                            <div class="form-group">
                                <label for="order-rfq-id">RFQ ID:</label>
                                <input type="text" id="order-rfq-id" placeholder="Server RFQ ID from submit_rfq response">
                            </div>
                        </div>

                        <!-- Cancel RFQ Parameters -->
                        <div id="cancel-rfq-params" class="message-params" style="display: none;">
                            <div class="form-group">
                                <label for="cancel-rfq-id">RFQ ID:</label>
                                <input type="text" id="cancel-rfq-id" placeholder="RFQ ID to cancel">
                            </div>
                        </div>

                        <!-- Custom Message Parameters -->
                        <div id="custom-params" class="message-params" style="display: none;">
                            <div class="form-group">
                                <label for="custom-message">Custom JSON Message:</label>
                                <textarea id="custom-message" rows="8" placeholder='{"command": "ping", "reqid": 1}'></textarea>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="request-id">Request ID:</label>
                            <input type="text" id="request-id" placeholder="Optional request ID">
                        </div>

                        <button id="send-message-btn" class="btn btn-primary">Send Message</button>
                    </div>
                </div>
            </div>

            <!-- Trading API Tab -->
            <div id="trading-tab" class="tab-content">
                <div class="panel">
                    <h2>Trading API</h2>
                    <div class="api-controls">
                        <h3>Execute Trade</h3>
                        <div class="form-group">
                            <label for="trade-request-id">Request ID:</label>
                            <input type="text" id="trade-request-id" placeholder="UUID for trade request">
                        </div>
                        <div class="form-group">
                            <label for="trade-symbol">Symbol:</label>
                            <input type="text" id="trade-symbol" placeholder="e.g., BTC-EUR" value="BTC-EUR">
                        </div>
                        <div class="form-group">
                            <label for="trade-quantity">Quantity:</label>
                            <input type="text" id="trade-quantity" placeholder="e.g., 0.01" value="0.01">
                        </div>
                        <div class="form-group">
                            <label for="trade-side">Side:</label>
                            <select id="trade-side">
                                <option value="Buy">Buy</option>
                                <option value="Sell">Sell</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="trade-price">Price:</label>
                            <input type="text" id="trade-price" placeholder="e.g., 42000.50">
                        </div>
                        <button id="execute-trade-btn" class="btn btn-success">Execute Trade</button>
                    </div>
                </div>
            </div>

            <!-- Withdrawals API Tab -->
            <div id="withdrawals-tab" class="tab-content">
                <div class="panel">
                    <h2>Withdrawals API</h2>
                    <div class="api-controls">
                        <h3>Execute Withdrawal</h3>
                        <div class="form-group">
                            <label for="withdrawal-request-id">Request ID:</label>
                            <input type="text" id="withdrawal-request-id" placeholder="UUID for withdrawal request">
                        </div>
                        <div class="form-group">
                            <label for="withdrawal-quantity">Quantity:</label>
                            <input type="text" id="withdrawal-quantity" placeholder="e.g., 1.0" value="1.0">
                        </div>
                        <div class="form-group">
                            <label for="withdrawal-currency">Currency:</label>
                            <input type="text" id="withdrawal-currency" placeholder="e.g., ETH" value="ETH">
                        </div>
                        <div class="form-group">
                            <label for="withdrawal-market-account">Market Account:</label>
                            <input type="text" id="withdrawal-market-account" value="default">
                        </div>
                        <div class="form-group">
                            <label for="withdrawal-type">Withdrawal Type:</label>
                            <select id="withdrawal-type">
                                <option value="crypto">Crypto</option>
                                <option value="fiat">Fiat</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="wallet-address">Wallet Address:</label>
                            <input type="text" id="wallet-address" placeholder="Destination wallet address">
                        </div>
                        <div class="form-group">
                            <label for="memo">Memo (optional):</label>
                            <input type="text" id="memo" placeholder="Optional memo">
                        </div>
                        <div class="form-group">
                            <label for="destination-tag">Destination Tag (optional):</label>
                            <input type="text" id="destination-tag" placeholder="Optional destination tag">
                        </div>
                        <div class="form-group">
                            <label for="callback-webhook-url">Callback Webhook URL (optional):</label>
                            <input type="text" id="callback-webhook-url" placeholder="https://example.com/webhook">
                        </div>
                        <button id="execute-withdrawal-btn" class="btn btn-success">Execute Withdrawal</button>

                        <h3>Webhook Management</h3>
                        <div class="button-group">
                            <button id="get-failed-webhooks-stats-btn" class="btn btn-info">Get Failed Webhooks Stats</button>
                            <button id="get-ready-for-retry-btn" class="btn btn-info">Get Ready for Retry</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Message Logs Tab -->
            <div id="logs-tab" class="tab-content">
                <div class="panel">
                    <h2>Message Logs</h2>
                    <div class="log-controls">
                        <button id="clear-logs-btn" class="btn btn-secondary">Clear Logs</button>
                        <label>
                            <input type="checkbox" id="auto-scroll-logs" checked> Auto-scroll
                        </label>
                    </div>
                    <div id="message-logs" class="message-logs">
                        <!-- Message logs will be displayed here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="application/dart" src="main.dart"></script>
</body>
</html>
