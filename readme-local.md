# NextGen Tradinf IF Server - Local Deployment Guide

This guide explains how to set up and run the NextGen WebSocket Server on your local machine for development purposes.

## Prerequisites

- Python 3.8 or higher
- pip (Python package manager)
- Git (optional, for cloning the repository)

## Setup Instructions

### 1. Clone or Download the Repository

```bash
git clone https://github.com/your-org/nextgen-websocket-server.git
cd nextgen-websocket-server
```

### 2. Install Dependencies

```bash
pip install -r requirements.txt
```

### 3. Configure Environment Variables

```bash
# Linux/macOS

# First 3 can be ommitted if you want to use default values 
export NG_WS_SERVER_HOST=0.0.0.0
export NG_WS_SERVER_PORT=8766
export NG_REST_SERVER_PORT=8081

# Vault and it's credentials (Mandatory)
export VAULT_HOST=https://dasp-vault-dev.nextgencoin.site/
export VAULT_TOKEN=your-vault-token

### 4. Start the Server

```bash
cd app/ng-ws-server
python fastapi_main.py
```

This will start:
- WebSocket server on specified port (requires jwt token in Authorization header) 
- REST API server on pspecified port (no authentication required)

## Verifying the Setup

### Check Server Status

You should see basic information about the server.

### Check Health Endpoint

```bash
curl http://localhost:8766/health/live
```

You should receive a response indicating the server is alive.

## API Documentation

Once the server is running, you can access the interactive API documentation:

- Swagger UI: http://localhost:8766/docs
- ReDoc: http://localhost:8766/redoc
