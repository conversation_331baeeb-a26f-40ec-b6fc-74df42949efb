import json
from .scrypt_websocket_client import ScryptWebsocketClient
from typing import List, Optional, Callable, Dict, Any
import datetime
import uuid
import logging

# Configure logging
logger = logging.getLogger(__name__)

class TreasuryClient:
    def __init__(self, client: ScryptWebsocketClient):
        """Initialize with an existing ScryptWebsocketClient instance."""
        self.client = client
        
    # ===== Balance Methods =====
    
    def subscribe_balances(self, 
                          currencies: Optional[List[str]] = None,
                          equivalent_currency: Optional[str] = None) -> int:
        """Subscribe to balance updates.
        
        Args:
            currencies: Optional list of currencies to filter balance updates by
            equivalent_currency: Optional currency to provide converted equivalent amounts in
            
        Returns:
            Request ID for the subscription
        """
        params = {}
        
        if currencies:
            params["Currencies"] = currencies
        if equivalent_currency:
            params["EquivalentCurrency"] = equivalent_currency
            
        return self.client.subscribe("Balance", **params)
    
    def subscribe_balance_transactions(self) -> int:
        """Subscribe to balance transaction updates.
        
        Returns:
            Request ID for the subscription
        """
        return self.client.subscribe("BalanceTransaction")
    
    def register_balance_callback(self, reqid: int, callback: Callable[[Dict[str, Any]], None]) -> None:
        """Register a callback for balance updates."""
        self.client.register_callback(reqid, callback)
    
    def register_balance_transaction_callback(self, reqid: int, callback: Callable[[Dict[str, Any]], None]) -> None:
        """Register a callback for balance transaction updates."""
        self.client.register_callback(reqid, callback)
    
    def new_withdraw_request(self,
                           currency: str,
                           quantity: str,
                           routing_info: Optional[Dict[str, Any]] = None,
                           address_id: Optional[str] = None,
                           cl_req_id: Optional[str] = None,
                           market_account: str = "default") -> Dict[str, Any]:
        """Request a new withdrawal.
        
        Args:
            currency: The currency that the quantity is specified in
            quantity: Requested quantity in units of Currency
            routing_info: Optional routing info for the transaction (e.g., wallet address)
            address_id: Optional address ID to attach to the withdraw transaction
            cl_req_id: Optional unique ID for this request (UUID will be generated if not provided)
            market_account: Optional market account to apply this transaction to (default: "default")
            
        Returns:
            Dictionary with the request details
        """
        if cl_req_id is None:
            cl_req_id = str(uuid.uuid4())
        
        if routing_info is not None and address_id is not None:
            raise ValueError("Cannot provide both routing_info and address_id")
            
        data = {
            "Currency": currency,
            "Quantity": quantity,
            "ClReqID": cl_req_id,
            "MarketAccount": market_account,
            "TransactTime": datetime.datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        }

        if routing_info:
            data["RoutingInfo"] = routing_info

        if address_id:
            data["AddressID"] = address_id
        
        request = {
            "reqid": self.client.request_counter,
            "type": "NewWithdrawRequest",
            "data": [data]
        }

        self.client.request_counter += 1

        self.client.ws.send(json.dumps(request))
        return request
    
    def withdraw_cancel_request(self, transaction_id: str) -> Dict[str, Any]:
        """Cancel a withdrawal.
        
        Args:
            transaction_id: ID of the transaction to cancel
            
        Returns:
            Dictionary with the request details
        """
        data = {
            "TransactionID": transaction_id
        }
            
        request = {
            "reqid": self.client.request_counter,
            "type": "WithdrawCancelRequest",
            "data": [data]
        }
        
        self.client.request_counter += 1
        self.client.ws.send(json.dumps(request))
        
        return request
    
    def new_deposit_request(self,
                          currency: str,
                          quantity: str,
                          tx_hashes: List[str],
                          cl_req_id: Optional[str] = None,
                          market_account: str = "default") -> Dict[str, Any]:
        """Request a new deposit.
        
        Args:
            currency: The currency that the quantity is specified in
            quantity: Requested quantity in units of Currency
            tx_hashes: List of blockchain transaction hashes associated with the deposit request
            cl_req_id: Optional unique ID for this request (UUID will be generated if not provided)
            market_account: Optional market account to apply this transaction to (default: "default")
            
        Returns:
            Dictionary with the request details
        """
        if cl_req_id is None:
            cl_req_id = str(uuid.uuid4())
            
        tx_hashes_objects = [{"TxHash": tx_hash} for tx_hash in tx_hashes]
        
        data = {
            "Currency": currency,
            "Quantity": quantity,
            "ClReqID": cl_req_id,
            "MarketAccount": market_account,
            "TxHashes": tx_hashes_objects,
            "TransactTime": datetime.datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        }
            
        request = {
            "reqid": self.client.request_counter,
            "type": "NewDepositRequest",
            "data": [data]
        }
        
        self.client.request_counter += 1
        self.client.ws.send(json.dumps(request))
        
        return request
    
    def get_balance(self, balance_data: List[Dict[str, Any]], currency: str) -> Optional[Dict[str, Any]]:
        """Extract balance information for a specific currency from balance data.
        
        Args:
            balance_data: List of balance data dictionaries
            currency: Currency to get balance for
            
        Returns:
            Balance dictionary for the specified currency, or None if not found
        """
        for balance in balance_data:
            if balance.get("Currency") == currency:
                return balance
        return None
    
    def get_available_amount(self, balance_data: Dict[str, Any]) -> str:
        """Extract the available amount from a balance data dictionary.
        
        Args:
            balance_data: Balance data dictionary
            
        Returns:
            Available amount as a string
        """
        return balance_data.get("AvailableAmount", "0")
    
    def get_total_amount(self, balance_data: Dict[str, Any]) -> str:
        """Extract the total amount from a balance data dictionary.
        
        Args:
            balance_data: Balance data dictionary
            
        Returns:
            Total amount as a string
        """
        return balance_data.get("Amount", "0")
    
    def get_equivalent_amount(self, balance_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Extract the equivalent amount information from a balance data dictionary.
        
        Args:
            balance_data: Balance data dictionary
            
        Returns:
            Equivalent amount dictionary, or None if not available
        """
        return balance_data.get("Equivalent")
    
    def is_transaction_pending(self, transaction_data: Dict[str, Any]) -> bool:
        """Check if a transaction is pending.
        
        Args:
            transaction_data: Transaction data dictionary
            
        Returns:
            True if the transaction is pending, False otherwise
        """
        status = transaction_data.get("Status")
        return status in ["PendingApproval", "PendingTransfer"]
    
    # ===== Credit/Exposure Methods =====
    
    def subscribe_exposure(self) -> int:
        """Subscribe to exposure updates.
        
        Returns:
            Request ID for the subscription
        """
        return self.client.subscribe("Exposure")
    
    def register_exposure_callback(self, reqid: int, callback: Callable[[Dict[str, Any]], None]) -> None:
        """Register a callback for exposure updates."""
        self.client.register_callback(reqid, callback)
    
    def get_exposure_currency(self, exposure_data: Dict[str, Any]) -> str:
        """Extract the exposure currency from exposure data.
        
        Args:
            exposure_data: Exposure data dictionary
            
        Returns:
            Exposure currency as a string
        """
        return exposure_data.get("ExposureCurrency", "")
    
    def get_current_exposure(self, exposure_data: Dict[str, Any]) -> str:
        """Extract the current exposure from exposure data.
        
        Args:
            exposure_data: Exposure data dictionary
            
        Returns:
            Current exposure as a string
        """
        return exposure_data.get("Exposure", "0")
    
    def get_exposure_limit(self, exposure_data: Dict[str, Any]) -> str:
        """Extract the exposure limit from exposure data.
        
        Args:
            exposure_data: Exposure data dictionary
            
        Returns:
            Exposure limit as a string
        """
        return exposure_data.get("ExposureLimit", "0")
    
    def get_exposure_status(self, exposure_data: Dict[str, Any]) -> str:
        """Extract the exposure status from exposure data.
        
        Args:
            exposure_data: Exposure data dictionary
            
        Returns:
            Exposure status as a string
        """
        return exposure_data.get("Status", "Unknown")
    
    def is_exposure_online(self, exposure_data: Dict[str, Any]) -> bool:
        """Check if exposure information is online.
        
        Args:
            exposure_data: Exposure data dictionary
            
        Returns:
            True if exposure information is online, False otherwise
        """
        return exposure_data.get("Status") == "Online"
    
    def get_exposure_timestamp(self, exposure_data: Dict[str, Any]) -> datetime.datetime:
        """Extract and parse the exposure timestamp.
        
        Args:
            exposure_data: Exposure data dictionary
            
        Returns:
            Datetime object representing the exposure timestamp
        """
        timestamp_str = exposure_data.get("Timestamp")
        if timestamp_str:
            return datetime.datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
        return datetime.datetime.now(datetime.timezone.utc)
    
    def get_exposure_age(self, exposure_data: Dict[str, Any]) -> datetime.timedelta:
        """Calculate the age of exposure data.
        
        Args:
            exposure_data: Exposure data dictionary
            
        Returns:
            Timedelta representing the age of the exposure data
        """
        exposure_time = self.get_exposure_timestamp(exposure_data)
        return datetime.datetime.now(datetime.timezone.utc) - exposure_time
    
    def calculate_exposure_percentage(self, exposure_data: Dict[str, Any]) -> float:
        """Calculate the percentage of exposure limit used.
        
        Args:
            exposure_data: Exposure data dictionary
            
        Returns:
            Percentage of exposure limit used as a float
        """
        exposure = float(self.get_current_exposure(exposure_data))
        limit = float(self.get_exposure_limit(exposure_data))
        
        if limit == 0:
            return 0.0
            
        return (exposure / limit) * 100.0
    
    def is_exposure_critical(self, exposure_data: Dict[str, Any], threshold_percentage: float = 90.0) -> bool:
        """Check if exposure is at a critical level.
        
        Args:
            exposure_data: Exposure data dictionary
            threshold_percentage: Percentage threshold to consider exposure critical (default: 90.0)
            
        Returns:
            True if exposure is at a critical level, False otherwise
        """
        percentage = self.calculate_exposure_percentage(exposure_data)
        return percentage >= threshold_percentage
    
    def get_remaining_exposure(self, exposure_data: Dict[str, Any]) -> float:
        """Calculate the remaining available exposure.
        
        Args:
            exposure_data: Exposure data dictionary
            
        Returns:
            Remaining available exposure as a float
        """
        exposure = float(self.get_current_exposure(exposure_data))
        limit = float(self.get_exposure_limit(exposure_data))
        
        return max(0.0, limit - exposure)
    
    def format_exposure_summary(self, exposure_data: Dict[str, Any]) -> str:
        """Format exposure data as a human-readable summary.
        
        Args:
            exposure_data: Exposure data dictionary
            
        Returns:
            Human-readable summary of exposure data
        """
        currency = self.get_exposure_currency(exposure_data)
        exposure = self.get_current_exposure(exposure_data)
        limit = self.get_exposure_limit(exposure_data)
        percentage = self.calculate_exposure_percentage(exposure_data)
        status = self.get_exposure_status(exposure_data)
        
        return (f"Exposure: {exposure} {currency} / {limit} {currency} "
                f"({percentage:.2f}%) - Status: {status}")
