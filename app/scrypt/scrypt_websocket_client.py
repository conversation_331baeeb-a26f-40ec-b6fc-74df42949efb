import asyncio
import os
import datetime
import hmac
import hashlib
import base64
import json
import threading
import logging
import time
from typing import Dict, Callable, Any
import websocket

# Import Vault client
from hcvault import get_vault_client

# Configure logging
logger = logging.getLogger(__name__)

vault_path = "kv/data/apps/trading-if"

class ScryptWebsocketClient:
    def __init__(self):
        """
        Initialize the Scrypt websocket client using Hashicorp Vault.

        Args:
            vault_path: Path to the secrets in Vault. Default is "kv/data/apps/scrypt-lp".
        """
        try:
            # Initialize Vault and load secrets
            vclient = get_vault_client()
            secrets = vclient.get_secret(vault_path)

            # Get configuration from Vault or use defaults
            SCRYPT_API_KEY = secrets.get("SCRYPT_API_KEY")
            SCRYPT_API_SECRET = secrets.get("SCRYPT_API_SECRET")
            SCRYPT_API_URL = secrets.get("SCRYPT_API_URL")
            SCRYPT_TIMEOUT = secrets.get("SCRYPT_TIMEOUT")
        except Exception as e:
            logger.warning(f"Failed to load Scrypt configuration from Vault: {str(e)}")
            raise

        # Initialize client properties
        self.api_key = SCRYPT_API_KEY
        self.api_secret = SCRYPT_API_SECRET
        self.host = SCRYPT_API_URL
        self.timeout = int(SCRYPT_TIMEOUT)

        self.path = "/ws/v1"
        self.ws = None
        self.session_id = None
        self.callbacks = {}
        self.running = False
        self.request_counter = 1

        # Log configuration
        logger.info(f"ScryptWebsocketClient initialized with host: {self.host}")


    def _generate_signature(self) -> Dict[str, str]:
        """Generate authentication headers with signature."""
        utc_now = datetime.datetime.now(datetime.timezone.utc)
        utc_datetime = utc_now.strftime("%Y-%m-%dT%H:%M:%S.000000Z")

        params = "\n".join([
            "GET",
            utc_datetime,
            self.host,
            self.path,
        ])

        hash_obj = hmac.new(
            self.api_secret.encode('ascii'),
            params.encode('ascii'),
            hashlib.sha256
        )
        signature = base64.urlsafe_b64encode(hash_obj.digest()).decode()

        return {
            "ApiKey": self.api_key,
            "ApiSign": signature,
            "ApiTimestamp": utc_datetime,
        }

    def connect(self) -> bool:
        """
        Establish connection to the Scrypt websocket endpoint.

        Returns:
            True if the connection was successful, False otherwise.
        """
        try:
            # Generate authentication headers
            headers = self._generate_signature()
            # headers = [f"{key}: {value}" for key, value in auth_headers.items()]

            # Connect to websocket
            logger.info(f"Connecting to Scrypt WebSocket server at wss://{self.host}{self.path}")
            # logger.info(f"Generated auth_headers: {auth_headers}")
            logger.info(f"Generated headers: {headers}")
            logger.info(f"Timeout: {self.timeout}")

            try:
                self.ws = websocket.create_connection(
                    f"wss://{self.host}{self.path}",
                    header=headers,
                    timeout=self.timeout
                )
            except Exception as conn_error:
                logger.error(f"Failed to establish WebSocket connection: {conn_error}")
                # Set ws to None to indicate connection failure
                self.ws = None
                return False

            # Process hello message
            try:
                hello_msg_raw = self.ws.recv()
                hello_msg = json.loads(hello_msg_raw)

                if hello_msg.get('type') == 'hello':
                    self.session_id = hello_msg.get('session_id')
                    logger.info(f"Connected to Scrypt websocket. Session ID: {self.session_id}")
                    return True
                else:
                    logger.error(f"Unexpected response from Scrypt server: {hello_msg}")
                    return False
            except Exception as hello_error:
                logger.error(f"Failed to receive hello message: {hello_error}")
                return False

        except Exception as e:
            logger.error(f"Failed to connect to Scrypt websocket: {e}")
            # Set ws to None to indicate connection failure
            self.ws = None
            return False

    def subscribe(self, stream_name: str, **params) -> int:
        """Subscribe to a specific data stream."""
        reqid = self.request_counter
        self.request_counter += 1

        request = {
            "reqid": reqid,
            "type": "subscribe",
            "streams": [
                {
                    "name": stream_name,
                    **params
                }
            ],
            "ts": datetime.datetime.now(datetime.timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.000000Z")
        }

        # Check if WebSocket connection exists
        if self.ws:
            try:
                self.ws.send(json.dumps(request))
                logger.info(f"Sent subscription request for stream: {stream_name}")
            except Exception as e:
                logger.warning(f"Failed to send subscription request: {e}")
        else:
            logger.warning(f"Cannot subscribe to {stream_name}: WebSocket connection not established")

        return reqid

    def register_callback(self, reqid: int, callback: Callable[[Dict[str, Any]], None]) -> None:
        """Register a callback function for a specific request ID."""
        self.callbacks[reqid] = callback
        
    def register_async_callback(self, reqid: int, callback: Callable[[Dict[str, Any]], Any]) -> None:
        """
        Register an async callback function for a specific request ID.
        The callback can be either a regular function or a coroutine function.
        """
        self.callbacks[reqid] = callback

    def cancel_subscription(self, reqid: int) -> None:
        """Cancel an existing subscription."""
        request = {
            "reqid": reqid,
            "type": "cancel"
        }

        # Check if WebSocket connection exists
        if self.ws:
            try:
                self.ws.send(json.dumps(request))
                logger.info(f"Sent cancellation request for subscription {reqid}")
            except Exception as e:
                logger.warning(f"Failed to send cancellation request: {e}")
        else:
            logger.warning(f"Cannot cancel subscription {reqid}: WebSocket connection not established")

        # Remove callback regardless of whether the cancellation request was sent
        if reqid in self.callbacks:
            del self.callbacks[reqid]

    def _message_handler(self) -> None:
        """Handle incoming websocket messages."""
        while self.running:
            try:
                # Check if WebSocket connection exists
                if not self.ws:
                    logger.warning("WebSocket connection lost, attempting to reconnect...")
                    if self.connect():
                        logger.info("Successfully reconnected to Scrypt WebSocket server")
                    else:
                        logger.error("Failed to reconnect to Scrypt WebSocket server")
                        # Sleep to avoid tight reconnection loop
                        time.sleep(5)
                        continue

                # Receive message
                message = self.ws.recv()
                data = json.loads(message)
                logger.info(f"Received message: {data}")
                reqid = data.get('reqid')

                # Log message type for debugging
                msg_type = data.get('type', 'unknown')
                logger.info(f"Received message of type: {msg_type}\n")
                #logger.info(f"Message data: {json.dumps(data, indent=2)}")

                # Process message if we have a callback registered
                if reqid in self.callbacks:
                    callback = self.callbacks[reqid]
                    if asyncio.iscoroutinefunction(callback):
                        # Schedule async callback to run in the event loop
                        asyncio.run_coroutine_threadsafe(callback(data), self.loop)
                    else:
                        # Execute synchronous callback directly
                        callback(data)

            except websocket.WebSocketConnectionClosedException:
                logger.warning("WebSocket connection closed, will attempt to reconnect")
                self.ws = None
                # Sleep to avoid tight reconnection loop
                time.sleep(5)

            except Exception as e:
                logger.error(f"Error handling message: {e}")
                if not self.running:
                    break

                # If we encounter an error with the WebSocket, mark it as closed
                self.ws = None
                # Sleep to avoid tight error loop
                time.sleep(1)

    def start(self) -> bool:
        """
        Start the websocket client.

        Returns:
            True if the connection was successful, False otherwise.
        """
        # Always attempt to connect, even if we already have a connection
        # This ensures we have a fresh connection when starting
        if not self.connect():
            logger.error("Failed to connect to Scrypt WebSocket server")
            return False

        self.running = True
        self.message_thread = threading.Thread(target=self._message_handler)
        self.message_thread.daemon = True
        self.message_thread.start()

        # Create an event loop for async callbacks if needed
        self.loop = asyncio.get_event_loop() if asyncio.get_event_loop().is_running() else asyncio.new_event_loop()
        
        return True

    def stop(self) -> None:
        """Stop the websocket client."""
        self.running = False
        if self.ws:
            self.ws.close()
            self.ws = None

    def create_async_callback_wrapper(self, async_func):
        """
        Create a wrapper for an async function to be used as a callback.
        
        Args:
            async_func: The async function to wrap.
            
        Returns:
            A synchronous function that schedules the async function.
        """
        def wrapper(data):
            asyncio.run_coroutine_threadsafe(async_func(data), self.loop)
        return wrapper
