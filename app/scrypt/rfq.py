from asyncio.log import logger
from .scrypt_websocket_client import ScryptWebsocketClient
from typing import List, Optional, Callable, Dict, Any
import datetime
import uuid
import json

class RFQClient:
    def __init__(self, client: ScryptWebsocketClient):
        """Initialize with an existing ScryptWebsocketClient instance."""
        self.client = client

    def subscribe_quotes(self,
                        start_date: Optional[str] = None,
                        end_date: Optional[str] = None,
                        symbol: Optional[str] = None,
                        rfq_id: Optional[str] = None) -> int:
        """Subscribe to quote updates.

        Args:
            start_date: Optional ISO-8601 UTC string to filter quotes for RFQs submitted after this time
            end_date: Optional ISO-8601 UTC string to filter quotes for RFQs submitted before this time
            symbol: Optional symbol to filter quotes by
            rfq_id: Optional RFQ ID to filter by

        Returns:
            Request ID for the subscription
        """
        params = {}

        if start_date:
            params["StartDate"] = start_date
        if end_date:
            params["EndDate"] = end_date
        if symbol:
            params["Symbol"] = symbol
        if rfq_id:
            params["RFQID"] = rfq_id

        return self.client.subscribe("Quote", **params)

    def register_quote_callback(self, reqid: int, callback: Callable[[Dict[str, Any]], None]) -> None:
        """Register a callback for quote updates."""
        self.client.register_callback(reqid, callback)

    def quote_request(self,
                     symbol: str,
                     currency: str,
                     order_qty: str,
                     side: Optional[str] = None,
                     quote_req_id: Optional[str] = None) -> Dict[str, Any]:
        """Request a quote thereby opening a new RFQ.

        Args:
            symbol: Symbol of the security to request a quote for
            currency: The currency that the quantity is specified in
            order_qty: Requested quantity in units of Currency
            side: Optional side for a one-sided request, either "Buy" or "Sell" (leave None for two-way quote)
            quote_req_id: Optional unique ID for this request (UUID will be generated if not provided)

        Returns:
            Dictionary with the request details
        """
        if quote_req_id is None:
            quote_req_id = str(uuid.uuid4())

        data = {
            "Symbol": symbol,
            "Currency": currency,
            "QuoteReqID": quote_req_id,
            "OrderQty": str(order_qty),
            "TransactTime": datetime.datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        }

        if side:
            if side not in ["Buy", "Sell"]:
                raise ValueError("Side must be either 'Buy' or 'Sell'")
            data["Side"] = side

        request = {
            "reqid": self.client.request_counter,
            "type": "QuoteRequest",
            "data": [data]
        }

        self.client.request_counter += 1
        logger.info(f"Sending quote request: {request}")
        # logger.info(f"Sending quote request: {data['Symbol']} - {data['OrderQty']} - {data['Side']} - request id{data['QuoteReqID']}")
        self.client.ws.send(json.dumps(request))

        return request

    def quote_cancel_request(self,
                           quote_req_id: str,
                           rfq_id: str) -> Dict[str, Any]:
        """Cancel an open RFQ.

        Args:
            quote_req_id: QuoteReqID from the request to cancel
            rfq_id: RFQID to cancel

        Returns:
            Dictionary with the request details
        """
        data = {
            "QuoteReqID": quote_req_id,
            "RFQID": rfq_id,
            "TransactTime": datetime.datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        }

        request = {
            "reqid": self.client.request_counter,
            "type": "QuoteCancelRequest",
            "data": [data]
        }

        logger.info(f"Sending quote cancel request: {request}")
        self.client.request_counter += 1
        self.client.ws.send(json.dumps(request))

        return request

    def is_quote_active(self, quote_data: Dict[str, Any]) -> bool:
        """Check if a quote is active (can be traded against).

        Args:
            quote_data: Quote data dictionary

        Returns:
            True if the quote is active, False otherwise
        """
        status = quote_data.get("QuoteStatus")
        return status == "Open"

    def get_quote_expiry(self, quote_data: Dict[str, Any]) -> datetime.datetime:
        """Extract and parse the quote expiry time.

        Args:
            quote_data: Quote data dictionary

        Returns:
            Datetime object representing the quote expiry time
        """
        valid_until_time = quote_data.get("ValidUntilTime")
        if valid_until_time:
            return datetime.datetime.fromisoformat(valid_until_time.replace('Z', '+00:00'))
        return datetime.datetime.utcnow()

    def is_quote_expired(self, quote_data: Dict[str, Any]) -> bool:
        """Check if a quote has expired.

        Args:
            quote_data: Quote data dictionary

        Returns:
            True if the quote has expired, False otherwise
        """
        expiry = self.get_quote_expiry(quote_data)
        return datetime.datetime.utcnow().replace(tzinfo=datetime.timezone.utc) > expiry

    def get_quote_details(self, quote_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract and return important quote details from quote data.

        Args:
            quote_data: Quote data dictionary

        Returns:
            Dictionary with key quote details
        """
        details = {
            "RFQID": quote_data.get("RFQID"),
            "QuoteID": quote_data.get("QuoteID"),
            "Symbol": quote_data.get("Symbol"),
            "Currency": quote_data.get("Currency"),
            "OrderQty": quote_data.get("OrderQty"),
            "QuoteStatus": quote_data.get("QuoteStatus"),
            "BidPx": quote_data.get("BidPx"),
            "BidAmt": quote_data.get("BidAmt"),
            "OfferPx": quote_data.get("OfferPx"),
            "OfferAmt": quote_data.get("OfferAmt"),
            "ValidUntilTime": quote_data.get("ValidUntilTime")
        }

        return {k: v for k, v in details.items() if v is not None}

    def get_best_price(self, quote_data: Dict[str, Any], side: str) -> Optional[str]:
        """Get the best price from a quote for a given side.

        Args:
            quote_data: Quote data dictionary
            side: "Buy" or "Sell"

        Returns:
            Best price as a string, or None if not available
        """
        if side == "Buy":
            return quote_data.get("OfferPx")  # When buying, use the offer price
        elif side == "Sell":
            return quote_data.get("BidPx")    # When selling, use the bid price
        else:
            raise ValueError("Side must be either 'Buy' or 'Sell'")

    def get_quote_amount(self, quote_data: Dict[str, Any], side: str) -> Optional[str]:
        """Get the amount from a quote for a given side.

        Args:
            quote_data: Quote data dictionary
            side: "Buy" or "Sell"

        Returns:
            Amount as a string, or None if not available
        """
        if side == "Buy":
            return quote_data.get("OfferAmt")  # When buying, use the offer amount
        elif side == "Sell":
            return quote_data.get("BidAmt")    # When selling, use the bid amount
        else:
            raise ValueError("Side must be either 'Buy' or 'Sell'")

    def submit_rfq(self, symbol: str, order_qty: str, side: str, rfq_id: str, quote_req_id: str) -> Dict[str, Any]:
        """
        Submit an RFQ to Scrypt.

        Args:
            symbol: The symbol.
            order_qty: The order quantity.
            side: The side (Buy or Sell).
            rfq_id: The RFQ ID.
            quote_req_id: The quote request ID.

        Returns:
            The response from Scrypt.
        """
        try:
            # In a real implementation, this would call the quote_request method
            # For now, we'll simulate a successful response

            return {
                "status": "success",
                "message": "RFQ submitted successfully",
                "data": {
                    "rfq_id": rfq_id,
                    "quote_req_id": quote_req_id
                }
            }

        except Exception as e:
            import logging
            logging.getLogger(__name__).error(f"Error submitting RFQ: {str(e)}")
            return {
                "status": "error",
                "message": f"Failed to submit RFQ: {str(e)}"
            }

    def cancel_rfq(self, rfq_id: str) -> Dict[str, Any]:
        """
        Cancel an RFQ.

        Args:
            rfq_id: The RFQ ID.

        Returns:
            The response from Scrypt.
        """
        try:
            # In a real implementation, this would call the quote_cancel_request method
            # For now, we'll simulate a successful response

            return {
                "status": "success",
                "message": "RFQ cancelled successfully",
                "data": {
                    "rfq_id": rfq_id
                }
            }

        except Exception as e:
            import logging
            logging.getLogger(__name__).error(f"Error cancelling RFQ: {str(e)}")
            return {
                "status": "error",
                "message": f"Failed to cancel RFQ: {str(e)}"
            }
