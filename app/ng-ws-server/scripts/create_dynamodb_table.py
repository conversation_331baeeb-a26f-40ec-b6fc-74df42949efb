#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create the DynamoDB table for withdrawal requests.

This script creates the necessary DynamoDB table with the correct schema
for storing withdrawal request data.
"""

import boto3
import logging
import sys
from botocore.exceptions import ClientError

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_withdrawal_table(table_name: str = "withdrawal-requests", region_name: str = "us-east-1"):
    """
    Create the DynamoDB table for withdrawal requests.
    
    Args:
        table_name: Name of the table to create
        region_name: AWS region to create the table in
    """
    try:
        # Initialize DynamoDB client
        dynamodb = boto3.client('dynamodb', region_name=region_name)
        
        # Define table schema
        table_definition = {
            'TableName': table_name,
            'KeySchema': [
                {
                    'AttributeName': 'client_withdrawal_id',
                    'KeyType': 'HASH'  # Primary key
                }
            ],
            'AttributeDefinitions': [
                {
                    'AttributeName': 'client_withdrawal_id',
                    'AttributeType': 'S'  # String
                }
            ],
            'BillingMode': 'PAY_PER_REQUEST',  # On-demand billing
            'Tags': [
                {
                    'Key': 'Service',
                    'Value': 'NextGenTradingServer'
                },
                {
                    'Key': 'Purpose',
                    'Value': 'WithdrawalRequestPersistence'
                }
            ]
        }
        
        # Create the table
        logger.info(f"Creating DynamoDB table: {table_name}")
        response = dynamodb.create_table(**table_definition)
        
        logger.info(f"Table creation initiated. Status: {response['TableDescription']['TableStatus']}")
        
        # Wait for table to be created
        logger.info("Waiting for table to become active...")
        waiter = dynamodb.get_waiter('table_exists')
        waiter.wait(TableName=table_name)
        
        logger.info(f"Table {table_name} created successfully!")
        
        # Print table information
        table_info = dynamodb.describe_table(TableName=table_name)
        logger.info(f"Table ARN: {table_info['Table']['TableArn']}")
        logger.info(f"Table Status: {table_info['Table']['TableStatus']}")
        
        return True
        
    except ClientError as e:
        error_code = e.response['Error']['Code']
        if error_code == 'ResourceInUseException':
            logger.warning(f"Table {table_name} already exists")
            return True
        else:
            logger.error(f"Failed to create table: {e}")
            return False
    except Exception as e:
        logger.error(f"Unexpected error creating table: {e}")
        return False


def delete_withdrawal_table(table_name: str = "withdrawal-requests", region_name: str = "us-east-1"):
    """
    Delete the DynamoDB table for withdrawal requests.
    
    WARNING: This will permanently delete all data in the table!
    
    Args:
        table_name: Name of the table to delete
        region_name: AWS region where the table exists
    """
    try:
        dynamodb = boto3.client('dynamodb', region_name=region_name)
        
        logger.warning(f"DELETING table {table_name} - this will permanently remove all data!")
        
        # Delete the table
        response = dynamodb.delete_table(TableName=table_name)
        logger.info(f"Table deletion initiated. Status: {response['TableDescription']['TableStatus']}")
        
        # Wait for table to be deleted
        logger.info("Waiting for table to be deleted...")
        waiter = dynamodb.get_waiter('table_not_exists')
        waiter.wait(TableName=table_name)
        
        logger.info(f"Table {table_name} deleted successfully!")
        return True
        
    except ClientError as e:
        error_code = e.response['Error']['Code']
        if error_code == 'ResourceNotFoundException':
            logger.warning(f"Table {table_name} does not exist")
            return True
        else:
            logger.error(f"Failed to delete table: {e}")
            return False
    except Exception as e:
        logger.error(f"Unexpected error deleting table: {e}")
        return False


def check_table_exists(table_name: str = "withdrawal-requests", region_name: str = "us-east-1"):
    """
    Check if the DynamoDB table exists.
    
    Args:
        table_name: Name of the table to check
        region_name: AWS region to check in
        
    Returns:
        True if table exists, False otherwise
    """
    try:
        dynamodb = boto3.client('dynamodb', region_name=region_name)
        
        response = dynamodb.describe_table(TableName=table_name)
        status = response['Table']['TableStatus']
        
        logger.info(f"Table {table_name} exists with status: {status}")
        return status == 'ACTIVE'
        
    except ClientError as e:
        error_code = e.response['Error']['Code']
        if error_code == 'ResourceNotFoundException':
            logger.info(f"Table {table_name} does not exist")
            return False
        else:
            logger.error(f"Error checking table: {e}")
            return False
    except Exception as e:
        logger.error(f"Unexpected error checking table: {e}")
        return False


def main():
    """Main function to handle command line arguments."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Manage DynamoDB table for withdrawal requests')
    parser.add_argument('action', choices=['create', 'delete', 'check'], 
                       help='Action to perform')
    parser.add_argument('--table-name', default='withdrawal-requests',
                       help='Name of the DynamoDB table (default: withdrawal-requests)')
    parser.add_argument('--region', default='us-east-1',
                       help='AWS region (default: us-east-1)')
    
    args = parser.parse_args()
    
    if args.action == 'create':
        success = create_withdrawal_table(args.table_name, args.region)
        sys.exit(0 if success else 1)
    elif args.action == 'delete':
        # Confirm deletion
        confirm = input(f"Are you sure you want to DELETE table {args.table_name}? This cannot be undone! (yes/no): ")
        if confirm.lower() == 'yes':
            success = delete_withdrawal_table(args.table_name, args.region)
            sys.exit(0 if success else 1)
        else:
            logger.info("Deletion cancelled")
            sys.exit(0)
    elif args.action == 'check':
        exists = check_table_exists(args.table_name, args.region)
        if exists:
            logger.info(f"Table {args.table_name} exists and is active")
            sys.exit(0)
        else:
            logger.info(f"Table {args.table_name} does not exist or is not active")
            sys.exit(1)


if __name__ == '__main__':
    main()
