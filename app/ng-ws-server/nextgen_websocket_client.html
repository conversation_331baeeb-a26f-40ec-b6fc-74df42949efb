<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NextGen WebSocket Client</title>
    <!-- SockJS and STOMP libraries -->
    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1.6.1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/stompjs@2.3.3/lib/stomp.min.js"></script>
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2ecc71;
            --error-color: #e74c3c;
            --warning-color: #f39c12;
            --dark-color: #34495e;
            --light-color: #ecf0f1;
            --border-color: #bdc3c7;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            padding: 20px;
        }
        
        h1, h2, h3 {
            color: var(--dark-color);
            margin-bottom: 15px;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #ccc;
            margin-right: 5px;
        }
        
        .status-indicator.connected {
            background-color: var(--secondary-color);
        }
        
        .status-indicator.disconnected {
            background-color: var(--error-color);
        }
        
        .status-text {
            font-size: 14px;
            font-weight: bold;
        }
        
        .container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .panel {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
            flex: 1;
            min-width: 300px;
        }
        
        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .log-panel {
            height: 500px;
            overflow-y: auto;
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
        }
        
        .message {
            margin-bottom: 8px;
            padding: 8px;
            border-radius: 4px;
            word-break: break-word;
        }
        
        .message-time {
            font-weight: bold;
            margin-right: 8px;
        }
        
        .sent {
            background-color: #e3f2fd;
            border-left: 4px solid var(--primary-color);
        }
        
        .received {
            background-color: #e8f5e9;
            border-left: 4px solid var(--secondary-color);
        }
        
        .error {
            background-color: #ffebee;
            border-left: 4px solid var(--error-color);
        }
        
        .system {
            background-color: #fff8e1;
            border-left: 4px solid var(--warning-color);
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: var(--dark-color);
        }
        
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }
        
        button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: background-color 0.3s;
            margin-right: 5px;
            margin-bottom: 5px;
        }
        
        button:hover {
            background-color: #2980b9;
        }
        
        button:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }
        
        .btn-success {
            background-color: var(--secondary-color);
        }
        
        .btn-success:hover {
            background-color: #27ae60;
        }
        
        .btn-danger {
            background-color: var(--error-color);
        }
        
        .btn-danger:hover {
            background-color: #c0392b;
        }
        
        .btn-warning {
            background-color: var(--warning-color);
        }
        
        .btn-warning:hover {
            background-color: #d35400;
        }
        
        .tabs {
            display: flex;
            margin-bottom: 15px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .tab {
            padding: 10px 15px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            margin-right: 5px;
        }
        
        .tab.active {
            border-bottom: 2px solid var(--primary-color);
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .subscription-params {
            display: none;
        }
        
        .alert {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
            font-weight: bold;
        }
        
        .alert-error {
            background-color: #ffebee;
            color: var(--error-color);
        }
        
        .alert-success {
            background-color: #e8f5e9;
            color: var(--secondary-color);
        }
        
        .alert-warning {
            background-color: #fff8e1;
            color: var(--warning-color);
        }
        
        .json-key {
            color: #f92672;
        }
        
        .json-value {
            color: #a6e22e;
        }
        
        .json-string {
            color: #e6db74;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }
            
            .panel {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>NextGen WebSocket Client</h1>
        <div>
            <span class="status-indicator disconnected" id="connectionStatus"></span>
            <span class="status-text" id="connectionStatusText">Disconnected</span>
        </div>
    </div>
    
    <div class="panel">
        <div class="panel-header">
            <h2>Connection</h2>
        </div>
        <div class="form-group">
            <label for="serverUrl">Server URL:</label>
            <input type="text" id="serverUrl" value="http://localhost:8766/ws" />
        </div>
        <div class="form-group">
            <label for="jwtToken">JWT Token:</label>
            <input type="text" id="jwtToken" placeholder="Your JWT token" />
        </div>
        <div class="button-group">
            <button id="connectBtn">Connect</button>
            <button id="disconnectBtn" disabled>Disconnect</button>
            <button id="pingBtn" disabled>Ping</button>
        </div>
    </div>
    
    <div class="container">
        <div class="panel">
            <div class="panel-header">
                <h2>Operations</h2>
            </div>
            
            <div class="tabs">
                <div class="tab active" data-tab="subscriptions">Subscriptions</div>
                <div class="tab" data-tab="rfq">RFQ</div>
                <div class="tab" data-tab="orders">Orders</div>
                <div class="tab" data-tab="custom">Custom</div>
            </div>
            
            <!-- Subscriptions Tab -->
            <div class="tab-content active" id="subscriptions">
                <div class="form-group">
                    <label for="subscriptionType">Subscription Type:</label>
                    <select id="subscriptionType">
                        <option value="quotes">Quotes</option>
                        <option value="trades">Trades</option>
                        <option value="balances">Balances</option>
                        <option value="exposures">Exposures</option>
                        <option value="securities">Securities</option>
                        <option value="currencies">Currencies</option>
                    </select>
                </div>
                
                <div id="quotesParams" class="subscription-params" style="display:block;">
                    <div class="form-group">
                        <label for="quoteSymbol">Symbol:</label>
                        <input type="text" id="quoteSymbol" placeholder="e.g. BTC-EUR" value="BTC-EUR" />
                    </div>
                </div>
                
                <div id="tradesParams" class="subscription-params">
                    <div class="form-group">
                        <label for="tradeStartDate">Start Date (optional):</label>
                        <input type="datetime-local" id="tradeStartDate" />
                    </div>
                </div>
                
                <div id="balancesParams" class="subscription-params">
                    <div class="form-group">
                        <label for="equivalentCurrency">Equivalent Currency (optional):</label>
                        <input type="text" id="equivalentCurrency" value="USD" />
                    </div>
                </div>
                
                <div id="securitiesParams" class="subscription-params">
                    <div class="form-group">
                        <label for="securitySymbols">Symbols (comma separated, optional):</label>
                        <input type="text" id="securitySymbols" placeholder="e.g. BTC-USD,ETH-USD" />
                    </div>
                </div>
                
                <div class="button-group">
                    <button id="subscribeBtn" disabled>Subscribe</button>
                    <button id="unsubscribeBtn" disabled>Unsubscribe</button>
                </div>
                
                <div class="form-group" style="margin-top: 20px;">
                    <label>Active Subscriptions:</label>
                    <div id="activeSubscriptions" style="padding: 10px; background-color: #f5f5f5; border-radius: 4px; min-height: 50px;">
                        No active subscriptions
                    </div>
                </div>
            </div>
            
            <!-- RFQ Tab -->
            <div class="tab-content" id="rfq">
                <div class="form-group">
                    <label for="rfqSymbol">Symbol:</label>
                    <input type="text" id="rfqSymbol" placeholder="e.g. BTC-EUR" value="BTC-EUR" />
                </div>
                
                <div class="form-group">
                    <label for="rfqCurrency">Currency (optional):</label>
                    <input type="text" id="rfqCurrency" placeholder="e.g. EUR" />
                </div>
                
                <div class="form-group">
                    <label for="rfqSide">Side:</label>
                    <select id="rfqSide">
                        <option value="Buy">Buy</option>
                        <option value="Sell">Sell</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="rfqQuantity">Quantity:</label>
                    <input type="text" id="rfqQuantity" placeholder="e.g. 0.01" value="0.01" />
                </div>
                
                <div class="form-group">
                    <label for="rfqClientId">Client RFQ ID (optional):</label>
                    <input type="text" id="rfqClientId" placeholder="Custom client RFQ ID" />
                </div>
                
                <button id="submitRfqBtn" disabled>Submit RFQ</button>
                
                <div class="form-group" style="margin-top: 20px;">
                    <label for="cancelRfqId">RFQ ID to Cancel:</label>
                    <input type="text" id="cancelRfqId" placeholder="RFQ ID from response" />
                </div>
                
                <button id="cancelRfqBtn" disabled>Cancel RFQ</button>
            </div>
            
            <!-- Orders Tab -->
            <div class="tab-content" id="orders">
                <div class="form-group">
                    <label for="orderRfqId">RFQ ID:</label>
                    <input type="text" id="orderRfqId" placeholder="RFQ ID from response" />
                </div>
                
                <button id="submitOrderBtn" disabled>Submit Order</button>
            </div>
            
            <!-- Custom Tab -->
            <div class="tab-content" id="custom">
                <div class="form-group">
                    <label for="customCommand">Command:</label>
                    <input type="text" id="customCommand" placeholder="e.g. ping" />
                </div>
                <div class="form-group">
                    <label for="customData">JSON Data:</label>
                    <textarea id="customData" rows="6" placeholder='{"key": "value"}'></textarea>
                </div>
                <button id="sendCustomBtn" disabled>Send Custom Message</button>
            </div>
        </div>
        
        <div class="panel">
            <div class="panel-header">
                <h2>Message Log</h2>
                <button id="clearLogBtn" class="btn-warning">Clear Log</button>
            </div>
            <div id="messageLog" class="log-panel"></div>
        </div>
    </div>
    
    <script>
        // Global variables
        let stompClient = null;
        let requestCounter = 1;
        let activeSubscriptions = {};
        let connected = false;
        
        // DOM elements
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const pingBtn = document.getElementById('pingBtn');
        const subscribeBtn = document.getElementById('subscribeBtn');
        const unsubscribeBtn = document.getElementById('unsubscribeBtn');
        const submitRfqBtn = document.getElementById('submitRfqBtn');
        const cancelRfqBtn = document.getElementById('cancelRfqBtn');
        const submitOrderBtn = document.getElementById('submitOrderBtn');
        const sendCustomBtn = document.getElementById('sendCustomBtn');
        const clearLogBtn = document.getElementById('clearLogBtn');
        const messageLog = document.getElementById('messageLog');
        const subscriptionType = document.getElementById('subscriptionType');
        const connectionStatus = document.getElementById('connectionStatus');
        const connectionStatusText = document.getElementById('connectionStatusText');
        const activeSubscriptionsDiv = document.getElementById('activeSubscriptions');
        
        // Tab functionality
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', function() {
                // Remove active class from all tabs and contents
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
                
                // Add active class to clicked tab
                this.classList.add('active');
                
                // Show corresponding content
                const tabId = this.getAttribute('data-tab');
                document.getElementById(tabId).classList.add('active');
            });
        });
        
        // Show/hide subscription params based on selection
        subscriptionType.addEventListener('change', function() {
            document.querySelectorAll('.subscription-params').forEach(el => el.style.display = 'none');
            const selectedType = this.value;
            const paramsDiv = document.getElementById(`${selectedType}Params`);
            if (paramsDiv) {
                paramsDiv.style.display = 'block';
            }
        });
        
        // Connect to WebSocket
        connectBtn.addEventListener('click', function() {
            const serverUrl = document.getElementById('serverUrl').value;
            const jwtToken = document.getElementById('jwtToken').value;
            
            try {
                // Create SockJS instance
                const socket = new SockJS(serverUrl);
                
                // Create STOMP client over SockJS
                stompClient = Stomp.over(socket);
                
                // Disable debug logging
                stompClient.debug = null;
                
                // Connect to the server
                const headers = {};
                if (jwtToken) {
                    headers['Authorization'] = `Bearer ${jwtToken}`;
                }
                
                logMessage('Connecting to WebSocket server...', 'system');
                
                stompClient.connect(headers, function(frame) {
                    logMessage('Connected to WebSocket server', 'system');
                    updateConnectionStatus(true);
                    
                    // Enable buttons
                    enableButtons(true);
                    
                    // Subscribe to system messages
                    stompClient.subscribe('/user/queue/messages', function(message) {
                        try {
                            const body = JSON.parse(message.body);
                            logMessage('Received system message: ' + message.body, 'received');
                        } catch (error) {
                            logMessage('Error parsing system message: ' + error.message, 'error');
                        }
                    });
                    
                }, function(error) {
                    logMessage('Connection error: ' + error, 'error');
                    updateConnectionStatus(false);
                    resetConnectionState();
                });
                
            } catch (error) {
                logMessage('Connection error: ' + error.message, 'error');
                updateConnectionStatus(false);
                resetConnectionState();
            }
        });
        
        // Disconnect from WebSocket
        disconnectBtn.addEventListener('click', function() {
            if (stompClient) {
                stompClient.disconnect(function() {
                    logMessage('Disconnected from WebSocket server', 'system');
                    updateConnectionStatus(false);
                    resetConnectionState();
                });
            }
        });
        
        // Ping
        pingBtn.addEventListener('click', function() {
            if (!checkConnection()) return;
            
            const message = {
                command: 'ping',
                reqid: requestCounter++
            };
            
            stompClient.send('/app/ping', {}, JSON.stringify(message));
            logMessage('Sent: ' + JSON.stringify(message), 'sent');
        });
        
        // Subscribe to a stream
        subscribeBtn.addEventListener('click', function() {
            if (!checkConnection()) return;
            
            const type = subscriptionType.value;
            let destination = '';
            let message = {
                command: '',
                reqid: requestCounter++,
                data: {}
            };
            
            switch (type) {
                case 'quotes':
                    const symbol = document.getElementById('quoteSymbol').value;
                    if (!symbol) {
                        logMessage('Symbol is required for quote subscription', 'error');
                        return;
                    }
                    destination = '/app/subscribe_quotes';
                    message.command = 'subscribe_quotes';
                    message.data = { symbol: symbol };
                    
                    // Subscribe to quote updates
                    const subId = stompClient.subscribe(`/topic/quotes/${symbol}`, function(message) {
                        try {
                            const body = JSON.parse(message.body);
                            logMessage('Received quote: ' + message.body, 'received');
                        } catch (error) {
                            logMessage('Error parsing quote: ' + error.message, 'error');
                        }
                    });
                    
                    activeSubscriptions[`quotes_${symbol}`] = {
                        id: subId,
                        type: 'quotes',
                        params: { symbol: symbol }
                    };
                    break;
                    
                case 'trades':
                    const startDate = document.getElementById('tradeStartDate').value;
                    destination = '/app/subscribe_trades';
                    message.command = 'subscribe_trades';
                    if (startDate) {
                        message.data = { 
                            startDate: new Date(startDate).toISOString()
                        }
                    }

                    // Subscribe to trade updates
                    const tradeSubId = stompClient.subscribe('/topic/trades', function(message) {
                        try {
                            const body = JSON.parse(message.body);
                            logMessage('Received trade: ' + message.body, 'received');
                        } catch (error) {
                            logMessage('Error parsing trade: ' + error.message, 'error');
                        }
                    });

                    activeSubscriptions['trades'] = {
                        id: tradeSubId,
                        type: 'trades',
                        params: startDate ? { startDate: startDate } : {}
                    };
                    break;

                case 'balances':
                    const equivalentCurrency = document.getElementById('equivalentCurrency').value;
                    destination = '/app/subscribe_balances';
                    message.command = 'subscribe_balances';
                    if (equivalentCurrency) {
                        message.data = { equivalentCurrency: equivalentCurrency };
                    }

                    // Subscribe to balance updates
                    const balanceSubId = stompClient.subscribe('/topic/balances', function(message) {
                        try {
                            const body = JSON.parse(message.body);
                            logMessage('Received balance: ' + message.body, 'received');
                        } catch (error) {
                            logMessage('Error parsing balance: ' + error.message, 'error');
                        }
                    });

                    activeSubscriptions['balances'] = {
                        id: balanceSubId,
                        type: 'balances',
                        params: equivalentCurrency ? { equivalentCurrency: equivalentCurrency } : {}
                    };
                    break;

                case 'exposures':
                    destination = '/app/subscribe_exposures';
                    message.command = 'subscribe_exposures';

                    // Subscribe to exposure updates
                    const exposureSubId = stompClient.subscribe('/topic/exposures', function(message) {
                        try {
                            const body = JSON.parse(message.body);
                            logMessage('Received exposure: ' + message.body, 'received');
                        } catch (error) {
                            logMessage('Error parsing exposure: ' + error.message, 'error');
                        }
                    });

                    activeSubscriptions['exposures'] = {
                        id: exposureSubId,
                        type: 'exposures',
                        params: {}
                    };
                    break;

                case 'securities':
                    const securitySymbols = document.getElementById('securitySymbols').value;
                    destination = '/app/subscribe_securities';
                    message.command = 'subscribe_securities';
                    if (securitySymbols) {
                        message.data = { symbols: securitySymbols.split(',').map(s => s.trim()) };
                    }

                    // Subscribe to security updates
                    const securitySubId = stompClient.subscribe('/topic/securities', function(message) {
                        try {
                            const body = JSON.parse(message.body);
                            logMessage('Received security: ' + message.body, 'received');
                        } catch (error) {
                            logMessage('Error parsing security: ' + error.message, 'error');
                        }
                    });

                    activeSubscriptions['securities'] = {
                        id: securitySubId,
                        type: 'securities',
                        params: securitySymbols ? { symbols: securitySymbols } : {}
                    };
                    break;

                case 'currencies':
                    destination = '/app/subscribe_currencies';
                    message.command = 'subscribe_currencies';

                    // Subscribe to currency updates
                    const currencySubId = stompClient.subscribe('/topic/currencies', function(message) {
                        try {
                            const body = JSON.parse(message.body);
                            logMessage('Received currency: ' + message.body, 'received');
                        } catch (error) {
                            logMessage('Error parsing currency: ' + error.message, 'error');
                        }
                    });

                    activeSubscriptions['currencies'] = {
                        id: currencySubId,
                        type: 'currencies',
                        params: {}
                    };
                    break;

                default:
                    logMessage(`Unknown subscription type: ${type}`, 'error');
                    return;
            }

            stompClient.send(destination, {}, JSON.stringify(message));
            logMessage('Sent: ' + JSON.stringify(message), 'sent');

            // Update active subscriptions display
            updateActiveSubscriptionsDisplay();
        });

        // Unsubscribe from a stream
        unsubscribeBtn.addEventListener('click', function() {
            if (!checkConnection()) return;

            const type = subscriptionType.value;
            let destination = '';
            let message = {
                command: '',
                reqid: requestCounter++,
                data: {}
            };
            let subscriptionKey = '';

            switch (type) {
                case 'quotes':
                    const symbol = document.getElementById('quoteSymbol').value;
                    if (!symbol) {
                        logMessage('Symbol is required for quote unsubscription', 'error');
                        return;
                    }
                    destination = '/app/unsubscribe_quotes';
                    message.command = 'unsubscribe_quotes';
                    message.data = { symbol: symbol };
                    subscriptionKey = `quotes_${symbol}`;
                    break;

                case 'trades':
                    destination = '/app/unsubscribe_trades';
                    message.command = 'unsubscribe_trades';
                    subscriptionKey = 'trades';
                    break;

                case 'balances':
                    destination = '/app/unsubscribe_balances';
                    message.command = 'unsubscribe_balances';
                    subscriptionKey = 'balances';
                    break;

                case 'exposures':
                    destination = '/app/unsubscribe_exposures';
                    message.command = 'unsubscribe_exposures';
                    subscriptionKey = 'exposures';
                    break;

                case 'securities':
                    destination = '/app/unsubscribe_securities';
                    message.command = 'unsubscribe_securities';
                    subscriptionKey = 'securities';
                    break;

                case 'currencies':
                    destination = '/app/unsubscribe_currencies';
                    message.command = 'unsubscribe_currencies';
                    subscriptionKey = 'currencies';
                    break;

                default:
                    logMessage(`Unknown subscription type: ${type}`, 'error');
                    return;
            }

            // Check if subscription exists
            if (activeSubscriptions[subscriptionKey]) {
                // Unsubscribe from STOMP subscription
                activeSubscriptions[subscriptionKey].id.unsubscribe();

                // Remove from active subscriptions
                delete activeSubscriptions[subscriptionKey];

                // Send unsubscribe message to server
                stompClient.send(destination, {}, JSON.stringify(message));
                logMessage('Sent: ' + JSON.stringify(message), 'sent');

                // Update active subscriptions display
                updateActiveSubscriptionsDisplay();
            } else {
                logMessage(`No active subscription found for ${type}`, 'error');
            }
        });

        // Submit RFQ
        submitRfqBtn.addEventListener('click', function() {
            if (!checkConnection()) return;

            const symbol = document.getElementById('rfqSymbol').value;
            const currency = document.getElementById('rfqCurrency').value;
            const side = document.getElementById('rfqSide').value;
            const quantity = document.getElementById('rfqQuantity').value;
            const clientId = document.getElementById('rfqClientId').value || `client-rfq-${Date.now()}`;

            if (!symbol) {
                logMessage('Symbol is required for RFQ', 'error');
                return;
            }

            if (!quantity) {
                logMessage('Quantity is required for RFQ', 'error');
                return;
            }

            const message = {
                command: 'submit_rfq',
                reqid: requestCounter++,
                data: {
                    symbol: symbol,
                    side: side,
                    order_qty: quantity,
                    quote_req_id: clientId
                }
            };

            if (currency) {
                message.data.currency = currency;
            }

            stompClient.send('/app/submit_rfq', {}, JSON.stringify(message));
            logMessage('Sent: ' + JSON.stringify(message), 'sent');
        });

        // Cancel RFQ
        cancelRfqBtn.addEventListener('click', function() {
            if (!checkConnection()) return;

            const rfqId = document.getElementById('cancelRfqId').value;

            if (!rfqId) {
                logMessage('RFQ ID is required to cancel an RFQ', 'error');
                return;
            }

            const message = {
                command: 'cancel_rfq',
                reqid: requestCounter++,
                data: {
                    rfq_id: rfqId
                }
            };

            stompClient.send('/app/cancel_rfq', {}, JSON.stringify(message));
            logMessage('Sent: ' + JSON.stringify(message), 'sent');
        });

        // Submit Order
        submitOrderBtn.addEventListener('click', function() {
            if (!checkConnection()) return;

            const rfqId = document.getElementById('orderRfqId').value;

            if (!rfqId) {
                logMessage('RFQ ID is required to submit an order', 'error');
                return;
            }

            const message = {
                command: 'submit_order',
                reqid: requestCounter++,
                data: {
                    RFQID: rfqId
                }
            };

            stompClient.send('/app/submit_order', {}, JSON.stringify(message));
            logMessage('Sent: ' + JSON.stringify(message), 'sent');
        });

        // Send custom message
        sendCustomBtn.addEventListener('click', function() {
            if (!checkConnection()) return;

            const command = document.getElementById('customCommand').value;
            let data = {};

            if (!command) {
                logMessage('Command is required', 'error');
                return;
            }

            try {
                const dataText = document.getElementById('customData').value;
                if (dataText) {
                    data = JSON.parse(dataText);
                }
            } catch (error) {
                logMessage('Invalid JSON data: ' + error.message, 'error');
                return;
            }

            const message = {
                command: command,
                reqid: requestCounter++,
                data: data
            };

            stompClient.send(`/app/${command}`, {}, JSON.stringify(message));
            logMessage('Sent: ' + JSON.stringify(message), 'sent');
        });

        // Clear log
        clearLogBtn.addEventListener('click', function() {
            messageLog.innerHTML = '';
        });

        // Helper functions
        function logMessage(message, type) {
            const now = new Date();
            const timeString = now.toLocaleTimeString() + '.' + now.getMilliseconds().toString().padStart(3, '0');

            const msgDiv = document.createElement('div');
            msgDiv.className = `message ${type}`;

            const timeSpan = document.createElement('span');
            timeSpan.className = 'message-time';
            timeSpan.textContent = timeString;

            msgDiv.appendChild(timeSpan);
            msgDiv.appendChild(document.createTextNode(' ' + message));

            messageLog.appendChild(msgDiv);
            messageLog.scrollTop = messageLog.scrollHeight;
        }

        function updateConnectionStatus(isConnected) {
            connected = isConnected;

            if (isConnected) {
                connectionStatus.classList.remove('disconnected');
                connectionStatus.classList.add('connected');
                connectionStatusText.textContent = 'Connected';
            } else {
                connectionStatus.classList.remove('connected');
                connectionStatus.classList.add('disconnected');
                connectionStatusText.textContent = 'Disconnected';
            }
        }

        function enableButtons(enabled) {
            disconnectBtn.disabled = !enabled;
            pingBtn.disabled = !enabled;
            subscribeBtn.disabled = !enabled;
            unsubscribeBtn.disabled = !enabled;
            submitRfqBtn.disabled = !enabled;
            cancelRfqBtn.disabled = !enabled;
            submitOrderBtn.disabled = !enabled;
            sendCustomBtn.disabled = !enabled;

            connectBtn.disabled = enabled;
        }

        function resetConnectionState() {
            // Reset connection state
            stompClient = null;
            connected = false;

            // Clear active subscriptions
            activeSubscriptions = {};
            updateActiveSubscriptionsDisplay();

            // Disable buttons
            enableButtons(false);
        }

        function checkConnection() {
            if (!stompClient || !connected) {
                logMessage('Not connected to WebSocket server', 'error');
                return false;
            }
            return true;
        }

        function updateActiveSubscriptionsDisplay() {
            if (Object.keys(activeSubscriptions).length === 0) {
                activeSubscriptionsDiv.textContent = 'No active subscriptions';
                return;
            }

            activeSubscriptionsDiv.innerHTML = '';

            for (const [key, subscription] of Object.entries(activeSubscriptions)) {
                const subDiv = document.createElement('div');
                subDiv.style.marginBottom = '5px';
                subDiv.style.padding = '5px';
                subDiv.style.backgroundColor = '#e3f2fd';
                subDiv.style.borderRadius = '3px';

                let paramsText = '';
                if (subscription.params) {
                    paramsText = Object.entries(subscription.params)
                        .map(([k, v]) => `${k}: ${v}`)
                        .join(', ');
                }

                subDiv.textContent = `${subscription.type}${paramsText ? ' (' + paramsText + ')' : ''}`;

                activeSubscriptionsDiv.appendChild(subDiv);
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Set default values
            const now = new Date();
            now.setMinutes(now.getMinutes() - 30); // 30 minutes ago

            const tradeStartDateInput = document.getElementById('tradeStartDate');
            if (tradeStartDateInput) {
                tradeStartDateInput.value = now.toISOString().slice(0, 16);
            }
        });
    </script>
</body>
</html>