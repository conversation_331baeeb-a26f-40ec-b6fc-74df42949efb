"""
WebSocket server implementation.

This module implements the WebSocket server that handles client connections and messages.
"""

import logging
import asyncio
import json
import os
import sys
import traceback
from typing import Dict, Any, Optional, List
import datetime
import uuid
import websockets

# Add the parent directory to the path to allow imports from app.hcvault
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from scrypt.rfq import RFQClient
from scrypt.scrypt_websocket_client import ScryptWebsocketClient

from config import get_config
import jwt
from jwt_auth import JWTValidator

from client_manager import ClientManager
from rfq_manager import RFQManager
from order_manager import OrderManager

# Import Vault client
from hcvault import get_vault_client
vault_path = "kv/data/apps/trading-if"

# Configure logging
logger = logging.getLogger(__name__)


class WebSocketServer:
    """
    WebSocket server implementation.
    """

    def __init__(self):
        """Initialize the WebSocket server."""
        self.config = get_config()
        self.client_manager = ClientManager()
        self.server = None
        self.subscribed_quote_symbols: Dict[str, str] = {} 
        self._scrypt_client = ScryptWebsocketClient()
        self._rfq_client = RFQClient(self._scrypt_client)
        self._rfq_manager = RFQManager(self.client_manager,self._rfq_client)
        self.client_timeout = 120  # 2 minutes in seconds
        
        vclient = get_vault_client()
        secrets = vclient.get_secret(vault_path)

        self.expected_jwt_issuer = secrets.get("EXPECTED_JWT_ISSUER")
        self.jwt_validator = JWTValidator(
            issuer=self.expected_jwt_issuer,
            audience=None,
            jwks_algorithms=["RS256", "RS512", "ES256"]
        )
        
    async def initialize(self) -> bool:
        """
        Initialize the server components.

        Returns:
            True if initialization was successful, False otherwise.
        """
        try:
            self._scrypt_client.start()
            
            # Start the background task to monitor inactive clients
            asyncio.create_task(self.monitor_inactive_clients())

            # Start the quote scanner
            # await self.quote_manager.start_quote_scanner()

            logger.info("WebSocket server initialized successfully with Scrypt connection")
            return True

        except Exception as e:
            logger.error(f"Error initializing WebSocket server: {str(e)}")
            return False

    async def start(self) -> None:
        """Start the WebSocket server."""
        try:
            # Start the WebSocket server
            self.server = await websockets.serve(
                self.handle_client,
                self.config.host,
                self.config.ws_port
            )

            logger.info(f"WebSocket server started on {self.config.host}:{self.config.ws_port}")

            # Keep the server running
            await self.server.wait_closed()

        except Exception as e:
            logger.error(f"Error starting WebSocket server: {str(e)}")

    async def stop(self) -> None:
        """Stop the WebSocket server."""
        try:
            # Stop the quote scanner
            # await self.quote_manager.stop_quote_scanner()

            # Stop the Scrypt proxy
            # self.scrypt_proxy.stop()
            logger.info("Stopping Scrypt client")
            self._scrypt_client.stop()

            # Close all client connections
            logger.info("Closing client connections")
            for client_id, client in list(self.client_manager.clients.items()):
                try:
                    await client.websocket.close()
                except:
                    pass

            # Close the server
            if self.server:
                self.server.close()
                await self.server.wait_closed()

            logger.info("WebSocket server stopped")

        except Exception as e:
            logger.error(f"Error stopping WebSocket server: {str(e)}")

    async def handle_client(self, websocket: websockets.WebSocketServerProtocol, path: str = None
                            # ,
                            # extra_headers: str = None,
                            # args: Dict[str, Any] = None,
                            # kwargs: Dict[str, Any] = None,
                            # client_id: str = None,
                            # payload: Dict[str, Any] = None,
                            # jwt_validation_options: Dict[str, bool] = None,
                            # auth_header: str = None,
                            # token: str = None,
                            # username: str = None,
                            # partner_id: str = None,
                            # e: Exception = None,
                            # user_id: str = None,
                            # company_id: str = None,
                            # client: str = None,
                            # message: str = None
                            ) -> None:
        logger.info(self.handle_client.__code__.co_varnames)
        # logger.info(f"New client connected from {websocket.remote_address}, path: {path}, extra_headers: {extra_headers}, args: {args}, kwargs: {kwargs}, client_id: {client_id}, payload: {payload}, jwt_validation_options: {jwt_validation_options}, auth_header: {auth_header}, token: {token}, username: {username}, partner_id: {partner_id}, e: {e}, user_id: {user_id}, company_id: {company_id}, client: {client}, message: {message}")

        # for key, value in vars(websocket).items():
        #     print(f"websocket. {key}: {value}")
        # logger.info(f"websocket.request: {websocket.request}")
        # logger.info(f"websocket.request.headers: {websocket.request.headers}")
        # logger.info(f"websocket.request.headers.path: {websocket.request.path}")
        # logger.info(f"Authorization header !!!: {websocket.request.path.replace("/?Authorization=Bearer%20", "")}")

        """
        Handle a client connection.

        Args:
            websocket: The WebSocket connection.
            path: The connection path.
        """
        client_id = None
        payload = None

        try:
            # Authenticate the client

            try:
                # Validate the token
                jwt_validation_options = {
                            'verify_signature': True,
                            'verify_exp': False,
                            'verify_iat': True,
                            'verify_iss': True,
                            'verify_aud': False,
                            'require_exp': True,
                            'require_iat': True,
                            'require_iss': True,
                }

                # auth_header = websocket.request_headers.get("Authorization", "")
                # if auth_header.startswith("Bearer "):
                #     token = auth_header[7:]  # Remove "Bearer " prefix
                token = str(websocket.request.path.replace("/?Authorization=Bearer%20", ""))
    
                payload = self.jwt_validator.validate_token(token,jwt_validation_options)
                username = payload["username"]
                partner_id = payload["partnerId"]
                print(f"User {username} authenticated. Client ID: {partner_id}")
                
            except Exception as e:
                logger.error(f"Authentication failed: {str(e)}")
                logger.error(traceback.format_exc())
                await websocket.send(json.dumps({
                    "status": "error",
                    "message": "Authentication failed",
                    "ts": datetime.datetime.now(datetime.timezone.utc).isoformat()
                }))
                return

            # Extract user information
            user_id = payload.get("sub")
            company_id = payload.get("partnerId")

            # Register the client
            client = await self.client_manager.add_client(websocket, user_id, company_id)
            client_id = client.id

            # Send welcome message
            await websocket.send(json.dumps({
                "type": "hello",
                "message": "Welcome to Next Generation trading server",
                "ts": datetime.datetime.now(datetime.timezone.utc).isoformat(),
                "session_id": client_id
            }))

            # Handle client messages
            async for message in websocket:
                await self.handle_message(client_id, message)

        except websockets.exceptions.ConnectionClosed:
            logger.info(f"Client connection closed: {client_id}")


        except Exception as e:
            logger.error(f"Error handling client: {str(e)}")
            logger.error(traceback.format_exc())

        finally:
            # Remove the client
            if client_id:
                await self.client_manager.remove_client(client_id)

    async def handle_message(self, client_id: str, message: str) -> None:
        """
        Handle a message from a client.

        Args:
            client_id: The client ID.
            message: The message from the client.
        """
        try:
            # Update client's last activity timestamp
            client = await self.client_manager.get_client(client_id)
            if client:
                #logger.info(f"Updating last activity for client {client_id}")
                client.last_activity = datetime.datetime.utcnow()
                #logger.info(f"Updating last activity for client {client_id} - Done")
                
            # Parse the message
            data = json.loads(message)

            # Extract command and request ID
            command = data.get("command")
            reqid = data.get("reqid")

            if not command:
                await self.send_error(client_id, reqid, "Command is required")
                return

            # Handle the command
            response = await self.process_command(client_id, command, data)

            # Add request ID to response if provided
            if reqid is not None:
                #logger.info(f"Adding reqid to the response: {reqid}")
                response["reqid"] = reqid

            # Add timestamp
            #logger.info(f"Adding timestamp to the response: {datetime.datetime.now(datetime.timezone.utc).isoformat()}")
            response["ts"] = datetime.datetime.now(datetime.timezone.utc).isoformat()

            # Send response to client
            if client:
                #logger.info(f"Sending response to client {client_id}")
                await client.send(response)
            else:
                logger.warning(f"No client to send response to ......")

        except json.JSONDecodeError:
            await self.send_error(client_id, None, "Invalid JSON")

        except Exception as e:
            logger.error(f"Error handling message: {str(e)}")
            logger.error(traceback.format_exc())
            await self.send_error(client_id, None, f"Internal server error: {str(e)}")

    async def process_command(self, client_id: str, command: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a command from a client.

        Args:
            client_id: The client ID.
            command: The command.
            data: The command data.

        Returns:
            The response to send to the client.
        """
        # Handle ping command
        if command == "ping":
            return {"type": "Pong", "message": "Pong"}

        # Handle quote subscription
        elif command == "subscribe_quotes":
            symbol = data.get("data", {}).get("symbol")
            if not symbol:
                return {"status": "error", "message": "Symbol is required"}

            # Check if we need to subscribe to Scrypt
            if symbol not in self.subscribed_quote_symbols:
                self.subscribed_quote_symbols[symbol] = None
                await self.client_manager.subscribe_to_symbol(client_id, symbol)
            
                # Get current timestamp for subscription
                now = datetime.datetime.now(datetime.timezone.utc)
                start_time = now.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
                
                quotes_req_id = self._rfq_client.subscribe_quotes(
                    start_date=start_time,
                    symbol=symbol
                )
                self.subscribed_quote_symbols[symbol] = quotes_req_id

                #async_callback = create_async_callback_wrapper(self._rfq_manager.handle_quote_response)
                self._rfq_client.register_quote_callback(quotes_req_id, self._rfq_manager.handle_quote_response)
                #handle_quote_data


            else:
                quotes_req_id = self.subscribed_quote_symbols[symbol]

            return {"status": "success", "symbol": symbol, "subscription_stream_id": quotes_req_id}

        # Handle quote unsubscription
        elif command == "unsubscribe_quotes":
            symbol = data.get("data", {}).get("symbol")
            if not symbol:
                return {"status": "error", "message": "Symbol is required"}

            await self.client_manager.unsubscribe_from_symbol(client_id, symbol)
            return {"status": "success", "symbol": symbol,"message": "You are unsubscribed from the symbol"}

        # Handle RFQ submission
        elif command == "submit_rfq":
            return await self._rfq_manager.handle_rfq_request(client_id, data.get("data", {}))

        # Handle RFQ cancellation
        elif command == "cancel_rfq":
            return await self._rfq_manager.handle_rfq_cancel(client_id, data.get("data", {}))

        # Handle order submission
        elif command == "submit_order":
            # Let us check if reffered quote is registered and is still valid
            rfqid = data.get("data", {}).get("RFQID")
            logger.info(f">>> submit_order request received for RFQID: {rfqid}, data: {data}")

            recorded_quote = await self.client_manager.get_recorded_quote(client_id, rfqid)
            if not recorded_quote:
                return {"status": "error", "message": "Quote not found"}
            
            logger.info(f"Recorded quote found: {json.dumps(recorded_quote, indent=4)}")

            orderInfo = await self.client_manager.get_order(client_id, rfqid)
            if orderInfo:
                logger.info(f"Order already exists for this RFQ: {orderInfo}")
                return {"status": "error", "message": "Order already exists for this RFQ"}

            if self._rfq_client.is_quote_active(recorded_quote):
                logger.info(f"- Quote is still active")
            else:
                logger.info(f"- Quote is no longer active")
            
            newOrderId = str(uuid.uuid4())
            pOrderData = {
                "order_id": newOrderId,
                "order_status": "New"
            }
            orderInfo = await self.client_manager.register_order(client_id, rfqid, pOrderData)
            return {"status": "success", "message": "TBD","order_info": orderInfo}
            # return await self._rfq_manager.handle_order_submission(client_id, data.get("data", {}))

        # Unknown command
        else:
            return {"status": "error", "message": f"Unknown command: {command}"}

    async def send_error(self, client_id: str, reqid: Optional[int], message: str) -> None:
        """
        Send an error message to a client.

        Args:
            client_id: The client ID.
            reqid: The request ID.
            message: The error message.
        """
        try:
            client = await self.client_manager.get_client(client_id)
            if client:
                error = {
                    "status": "error",
                    "message": message,
                    "ts": datetime.datetime.now(datetime.timezone.utc).isoformat()
                }

                if reqid is not None:
                    error["reqid"] = reqid

                await client.send(error)

        except Exception as e:
            logger.error(f"Error sending error message: {str(e)}")

    async def monitor_inactive_clients(self) -> None:
        """
        Background task that monitors and disconnects inactive clients.
        A client is considered inactive if it hasn't sent a message in self.client_timeout seconds.
        """
        logger.info(f"Starting inactive client monitor task (timeout: {self.client_timeout} seconds)")
        while True:
            try:
                now = datetime.datetime.utcnow()
                inactive_clients = []
                
                # Check each client's last activity time
                for client_id, client in list(self.client_manager.clients.items()):
                    if client.last_activity:
                        inactive_time = (now - client.last_activity).total_seconds()
                        if inactive_time > self.client_timeout:
                            inactive_clients.append(client_id)
                
                # Disconnect inactive clients
                for client_id in inactive_clients:
                    logger.info(f"Disconnecting inactive client {client_id} (no activity for {self.client_timeout} seconds)")
                    client = await self.client_manager.get_client(client_id)
                    if client and client.websocket:
                        try:
                            # Send a timeout message before disconnecting
                            await client.send({
                                "status": "error",
                                "message": f"Session timeout after {self.client_timeout} seconds of inactivity",
                                "code": "session_timeout",
                                "ts": datetime.datetime.now(datetime.timezone.utc).isoformat()
                            })
                            await client.websocket.close(1000, "Session timeout")
                        except:
                            pass
                    
                    # Remove the client
                    await self.client_manager.remove_client(client_id)
                
                # Wait for 30 seconds before the next check
                await asyncio.sleep(30)
                
            except asyncio.CancelledError:
                logger.info("Inactive client monitor task cancelled")
                break
            except Exception as e:
                logger.error(f"Error in inactive client monitor: {str(e)}")
                # Continue running despite errors
                await asyncio.sleep(30)
