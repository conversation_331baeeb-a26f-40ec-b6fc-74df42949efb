"""
Withdrawal API endpoints.

This module provides endpoints for withdrawal requests and management.
"""
import logging
from datetime import datetime
from fastapi import APIRout<PERSON>, Depends, HTTPException, Head<PERSON>
from typing import Optional

from models.api_models import WithdrawalRequest
from services.withdrawal_service import get_withdrawal_execution_service
from services.withdrawal_dynamodb_service import get_withdrawal_dynamodb_service

logger = logging.getLogger(__name__)

router = APIRouter(tags=["withdrawals"])

@router.post("/withdrawal")
async def execute_trade(
    request: WithdrawalRequest):
    try:
        logger.info(f"Withdrawal execution request received: {request.request_id}")

        # Create trade execution service
        withdrawal_service = get_withdrawal_execution_service()

        # Execute trade with full validation
        response_data, status_code = await withdrawal_service.execute_withdrawal_request(request)

        # Return response with appropriate status code
        if status_code == 201:
            return response_data
        elif status_code == 400:
            raise HTTPException(status_code=400, detail=response_data)
        else:
            raise HTTPException(status_code=500, detail=response_data)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Withdrawal submisison execution error: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "Timestamp": datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                "result": "error",
                "message": f"Internal error during withdrawal request execution: {str(e)}"
            }
        )


@router.get("/failed-webhooks/stats")
async def get_failed_webhooks_stats():
    """
    Get statistics about failed webhooks.

    Returns:
        Dict: Statistics about failed webhooks including counts, distributions, and timing information
    """
    try:
        logger.info("Failed webhooks statistics request received")

        # Get the DynamoDB service
        dynamodb_service = get_withdrawal_dynamodb_service()

        # Get summary statistics
        stats = dynamodb_service.get_failed_webhooks_summary_stats()

        return {
            "timestamp": datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
            "result": "success",
            "data": stats
        }

    except Exception as e:
        logger.error(f"Error retrieving failed webhooks statistics: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "timestamp": datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                "result": "error",
                "message": f"Internal error retrieving failed webhooks statistics: {str(e)}"
            }
        )


@router.get("/failed-webhooks/ready-for-retry")
async def get_failed_webhooks_ready_for_retry():
    """
    Get failed webhooks that are ready for retry.

    Returns:
        Dict: List of withdrawal IDs that are ready for retry
    """
    try:
        logger.info("Failed webhooks ready for retry request received")

        # Get the DynamoDB service
        dynamodb_service = get_withdrawal_dynamodb_service()

        # Get webhooks ready for retry
        ready_df = dynamodb_service.get_failed_webhooks_ready_for_retry()

        if ready_df is None or ready_df.empty:
            return {
                "timestamp": datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                "result": "success",
                "data": {
                    "ready_for_retry_count": 0,
                    "withdrawal_ids": []
                }
            }

        # Extract withdrawal IDs
        withdrawal_ids = ready_df['client_withdrawal_id'].tolist()

        return {
            "timestamp": datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
            "result": "success",
            "data": {
                "ready_for_retry_count": len(withdrawal_ids),
                "withdrawal_ids": withdrawal_ids
            }
        }

    except Exception as e:
        logger.error(f"Error retrieving failed webhooks ready for retry: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "timestamp": datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                "result": "error",
                "message": f"Internal error retrieving failed webhooks ready for retry: {str(e)}"
            }
        )