"""
Statistics API endpoints.

This module provides endpoints for retrieving server and connection statistics.
"""

import time
import psutil
import threading
import logging
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException
from typing import Dict, Any

from models.api_models import StatsResponse, WebSocketStats, ScryptConnectionStats, SystemStats
from services.shared_state import get_shared_state, SharedState

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/stats", tags=["statistics"])


@router.get("/", response_model=StatsResponse)
async def get_server_stats(shared_state: SharedState = Depends(get_shared_state)) -> StatsResponse:
    """
    Get comprehensive server statistics.
    
    Returns:
        StatsResponse: Detailed server statistics including WebSocket, Scrypt, and system metrics
    """
    try:
        current_time = time.time()
        
        # WebSocket server statistics
        websocket_stats = await _get_websocket_stats(shared_state, current_time)
        
        # Scrypt connection statistics
        scrypt_stats = await _get_scrypt_stats(shared_state, current_time)
        
        # System statistics
        system_stats = await _get_system_stats()
        
        return StatsResponse(
            timestamp=datetime.utcnow(),
            websocket_server=websocket_stats,
            scrypt_connection=scrypt_stats,
            system=system_stats
        )
        
    except Exception as e:
        logger.error(f"Failed to get server stats: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve statistics: {str(e)}")


@router.get("/websocket")
async def get_websocket_stats(shared_state: SharedState = Depends(get_shared_state)) -> Dict[str, Any]:
    """
    Get WebSocket server specific statistics.
    
    Returns:
        Dict: WebSocket server statistics
    """
    try:
        current_time = time.time()
        return await _get_websocket_stats(shared_state, current_time)
    except Exception as e:
        logger.error(f"Failed to get WebSocket stats: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve WebSocket statistics: {str(e)}")


@router.get("/scrypt")
async def get_scrypt_stats(shared_state: SharedState = Depends(get_shared_state)) -> Dict[str, Any]:
    """
    Get Scrypt connection specific statistics.
    
    Returns:
        Dict: Scrypt connection statistics
    """
    try:
        current_time = time.time()
        return await _get_scrypt_stats(shared_state, current_time)
    except Exception as e:
        logger.error(f"Failed to get Scrypt stats: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve Scrypt statistics: {str(e)}")


@router.get("/system")
async def get_system_stats() -> Dict[str, Any]:
    """
    Get system resource statistics.
    
    Returns:
        Dict: System resource statistics
    """
    try:
        return await _get_system_stats()
    except Exception as e:
        logger.error(f"Failed to get system stats: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve system statistics: {str(e)}")


async def _get_websocket_stats(shared_state: SharedState, current_time: float) -> Dict[str, Any]:
    """Get WebSocket server statistics."""
    stats = {
        "connected_clients": 0,
        "active_rfqs": 0,
        "subscribed_symbols": 0,
        "uptime_seconds": current_time - shared_state.start_time,
        "server_running": False
    }
    
    if shared_state.websocket_server:
        ws_server = shared_state.websocket_server
        
        # Check if server is running
        stats["server_running"] = hasattr(ws_server, 'server') and ws_server.server is not None
        
        # Get client manager stats
        if hasattr(ws_server, 'client_manager') and ws_server.client_manager:
            stats["connected_clients"] = len(ws_server.client_manager.clients)
        
        # Get RFQ manager stats
        if hasattr(ws_server, '_rfq_manager') and ws_server._rfq_manager:
            stats["active_rfqs"] = len(ws_server._rfq_manager.active_rfqs)
        
        # Get subscribed symbols
        if hasattr(ws_server, 'subscribed_quote_symbols'):
            stats["subscribed_symbols"] = len(ws_server.subscribed_quote_symbols)
        
        # Additional server info
        if hasattr(ws_server, 'config'):
            stats["host"] = ws_server.config.host
            stats["ws_port"] = ws_server.config.ws_port
            stats["rest_port"] = ws_server.config.rest_port
    
    return stats


async def _get_scrypt_stats(shared_state: SharedState, current_time: float) -> Dict[str, Any]:
    """Get Scrypt connection statistics."""
    stats = {
        "is_connected": False,
        "connection_uptime_seconds": 0,
        "last_heartbeat": None,
        "websocket_url": None
    }
    
    if (shared_state.websocket_server and 
        hasattr(shared_state.websocket_server, '_scrypt_client')):
        scrypt_client = shared_state.websocket_server._scrypt_client
        
        # Check connection status
        if hasattr(scrypt_client, 'ws') and scrypt_client.ws:
            stats["is_connected"] = True
            
        # Get connection details
        if hasattr(scrypt_client, 'websocket_url'):
            stats["websocket_url"] = scrypt_client.websocket_url
            
        # Calculate uptime (simplified - would need to track connection start time)
        if stats["is_connected"]:
            stats["connection_uptime_seconds"] = current_time - shared_state.start_time
    
    return stats


async def _get_system_stats() -> Dict[str, Any]:
    """Get system resource statistics."""
    try:
        # Memory statistics
        memory = psutil.virtual_memory()
        
        # CPU statistics
        cpu_percent = psutil.cpu_percent(interval=0.1)
        
        # Thread count
        active_threads = threading.active_count()
        
        # Process information
        process = psutil.Process()
        
        stats = {
            "memory_usage_mb": round(memory.used / 1024 / 1024, 2),
            "memory_total_mb": round(memory.total / 1024 / 1024, 2),
            "memory_percent": memory.percent,
            "cpu_usage_percent": cpu_percent,
            "cpu_count": psutil.cpu_count(),
            "active_threads": active_threads,
            "process_id": process.pid,
            "process_memory_mb": round(process.memory_info().rss / 1024 / 1024, 2),
            "process_cpu_percent": process.cpu_percent(),
            "open_files": len(process.open_files()) if hasattr(process, 'open_files') else 0
        }
        
        # Disk usage for current directory
        try:
            disk_usage = psutil.disk_usage('.')
            stats["disk_usage_gb"] = round(disk_usage.used / 1024 / 1024 / 1024, 2)
            stats["disk_total_gb"] = round(disk_usage.total / 1024 / 1024 / 1024, 2)
            stats["disk_percent"] = round((disk_usage.used / disk_usage.total) * 100, 2)
        except Exception as e:
            logger.warning(f"Could not get disk usage: {e}")
        
        return stats
        
    except Exception as e:
        logger.error(f"Failed to get system stats: {e}")
        return {
            "error": str(e),
            "memory_usage_mb": 0,
            "cpu_usage_percent": 0,
            "active_threads": 0
        }
