"""
RFQ manager for the WebSocket server.

This module handles RFQ requests and cancellations.
"""

from decimal import Decimal
import logging
import asyncio
import json
import os
import sys
from typing import Dict, Set, List, Any, Optional
from datetime import datetime
import uuid
import traceback

from client_manager import ClientManager
# Add the parent directory to the path to allow imports from app.hcvault
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from scrypt.rfq import RFQClient
from auth import get_company_spread

# Configure logging
logger = logging.getLogger(__name__)


class RFQManager:
    """
    Manages RFQ requests and responses.
    """

    def __init__(self, client_manager: ClientManager, rfq_client: RFQClient):
        """
        Initialize the RFQ manager.

        Args:
            client_manager: The client manager.
            scrypt_proxy: The Scrypt proxy.
        """
        self.client_manager = client_manager
        self.rfq_client = rfq_client
        self.active_rfqs = {}  # Map of server RFQ IDs to RFQ data
        self.lock = asyncio.Lock()

    async def handle_rfq_request(self, client_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle an RFQ request from a client.

        Args:
            client_id: The client ID.
            data: The RFQ request data.

        Returns:
            The response to send to the client.
        """
        try:
            # Extract RFQ parameters
            symbol = data.get("symbol")
            currency = data.get("currency")
            order_qty = data.get("order_qty")
            side = data.get("side", "Buy")  # Default to Buy if not specified
            client_rfq_id = data.get("quote_req_id", str(uuid.uuid4()))

            # Validate parameters
            if not symbol:
                return {"status": "error", "message": "Symbol is required"}

            if not order_qty:
                return {"status": "error", "message": "Order quantity is required"}

            logger.info(f"RFQ received for: {symbol}, quantity: {order_qty}, side: {side}, client_rfq_id: {client_rfq_id}")

            # Submit RFQ to Scrypt
            response = self.rfq_client.quote_request(
                symbol,
                currency,
                order_qty,
                side,
                client_rfq_id
            )
            #logger.info(f"self.rfq_client.quote_request response: {response}")
            await self.client_manager.register_rfq(client_id, client_rfq_id)

            return response

        except Exception as e:
            logger.error(f"Error handling RFQ request: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                "status": "error",
                "message": f"Failed to process RFQ request: {str(e)}"
            }

    async def handle_rfq_cancel(self, client_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle an RFQ cabcellation request from a client.

        Args:
            client_id: The client ID.
            data: The RFQ request data.

        Returns:
            The response to send to the client.
        """
        try:
            # Extract RFQ parameters
            logger.info(f"!!! data: {data}")
            client_rfq_id = data.get("quote_req_id", str(uuid.uuid4()))
            rfq_id = data.get("rfq_id")

            logger.info(f"RFQ received, data: {data}")

            # Submit RFQ to Scrypt
            response = self.rfq_client.quote_cancel_request(
                client_rfq_id,
                rfq_id
            )
            #logger.info(f"self.rfq_client.quote_request response: {response}")
            await self.client_manager.register_rfq(client_id, client_rfq_id)

            return response

        except Exception as e:
            logger.error(f"Error handling RFQ request: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                "status": "error",
                "message": f"Failed to process RFQ request: {str(e)}"
            }


    async def handle_quote_response(self, quote_data: Dict[str, Any]):
        """
        Handle a quote response from Scrypt.

        Args:
            quote_data: The quote data from Scrypt.
        """
        try:
            #logger.info("QUOTE UPDATE\n")
            #logger.info(json.dumps(quote_data, indent=2))
            rfq_id = None
            RFQID = None
            if "data" in quote_data and len(quote_data["data"]) > 0:
                logger.info(f"Receiving quote response: {quote_data}")
                data = quote_data["data"][0]
                # Store the RFQID and QuoteID when received
                if "QuoteReqID" in data:
                    rfq_id = data["QuoteReqID"]
                if "RFQID" in data:
                    RFQID = data["RFQID"]

            if rfq_id:
                logger.info(f"QUOTE Update received for RFQ ID: {rfq_id}")

                # Get client
                client = await self.client_manager.get_client_for_rfq(rfq_id)

                if not client:
                    logger.warning(f"No client found for RFQ {rfq_id}")
                    return
                # Check if maybe trade already has happened - if so skip

                orderInfo = await self.client_manager.get_order(client.id, RFQID)
                if orderInfo:
                    logger.info(f"Order already exists for this RFQ with RFQID: {RFQID}")
                    return
                
                spread = get_company_spread(client.company_id)
                logger.info(f"Client found for RFQ {rfq_id} > {client.id} |Company : {client.company_id} Spread: {spread}")
                # Apply spread based on company ID

                logger.info(f"Original quote : {data['Symbol']} | {data['OrderQty']} | {data['Side']} | OfferPx: {data.get('OfferPx','n/a')} | BidPx: {data.get('BidPx','n/a')} | OfferAmt: {data.get('OfferAmt','n/a')} | BidAmt: {data.get('BidAmt','n/a')}")
                adjusted_quote = self._apply_spread(quote_data, spread)
                new_data = adjusted_quote["data"][0]
                logger.info(f"Adjusted quote : {new_data['Symbol']} | {new_data['OrderQty']} | {new_data['Side']} | OfferPx: {new_data.get('OfferPx','n/a')} | BidPx: {new_data.get('BidPx','n/a')} | OfferAmt: {new_data.get('OfferAmt','n/a')} | BidAmt: {new_data.get('BidAmt','n/a')}")

                # remove internal information
                adjusted_quote["data"][0].pop("CustomerUser", None)
                adjusted_quote["data"][0].pop("MarketAccount", None)
                
                # Let us record quote sent to client so we can check it when order request comes in
                await self.client_manager.record_quote(client.id, adjusted_quote["data"][0]["RFQID"],adjusted_quote["data"][0])

                #logger.info(f"Adjusted quote: {adjusted_quote}")

                # Update RFQ status
                #async with self.lock:
                #    if rfq_id in self.active_rfqs:
                #        self.active_rfqs[rfq_id]["status"] = "quoted"
                #        self.active_rfqs[rfq_id]["quote_id"] = adjusted_quote.get("QuoteID")
                #        self.active_rfqs[rfq_id]["quote_time"] = datetime.utcnow().isoformat()

                # Create message to send to client
                message = {
                    "type": "Quote",
                    "data": adjusted_quote,
                    "ts": datetime.utcnow().isoformat()
                }

                # Send message to client
                await client.send(message)
            return
        except Exception as e:
            logger.error(f"Error handling quote response: {str(e)}")

    def _apply_spread(self, quote_data: Dict[str, Any], spread: int) -> Dict[str, Any]:
        """
        Apply a spread to a quote.

        Args:
            quote_data: The original quote data.
            spread: The spread to apply (as a percentage).

        Returns:
            The quote data with spread applied.
        """
        # Create a copy of the quote data
        quote = quote_data.copy()

        if "data" in quote and len(quote["data"]) > 0:
            data = quote["data"][0]

            # Apply spread to price

            side = data.get("Side", "Buy")

            bid_price = data.get("BidPx")
            offer_price = data.get("OfferPx")

            #logger.info(f"Adjusting quoteSide: {side} BidPx: {bid_price} OfferPx: {offer_price}")

            if side == "Buy" and offer_price:
                price = float(offer_price)
                adjusted_price = price * (1 + spread / 10000)
                adjusted_offer_amnt = float(data.get("OrderQty"))*adjusted_price
                adjusted_offer_amnt = round(adjusted_offer_amnt, 2)
                data["OfferAmt"] = str(adjusted_offer_amnt)

                #logger.info(f"Adjusted offer price: {str(adjusted_price)}")
                data["OfferPx"] = str(adjusted_price)

            if side == "Sell" and bid_price:
                price = float(bid_price)
                adjusted_price = price * (1 + spread / 10000)
                adjusted_bid_amnt = float(data.get("OrderQty"))*adjusted_price
                adjusted_bid_amnt = round(adjusted_bid_amnt, 2)
                data["BidAmt"] = str(adjusted_bid_amnt)

                #logger.info(f"Adjusted bid price: {str(adjusted_price)}")
                data["BidPx"] = str(adjusted_price)
            
            quote["data"][0] = data

        return quote
