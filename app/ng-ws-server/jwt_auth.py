import jwt
import requests
from jwt import PyJWKClient
from datetime import datetime, timezone, timedelta
import logging
from typing import Dict, Any, List, Optional

class JWTValidator:
    def __init__(self, issuer: str, audience: Optional[str] = None,jwks_algorithms: Optional[List[str]] = ["RS256", "RS512", "ES256"]):
        """
        Initialize JWT validator
        
        Args:
            issuer: Expected JWT issuer (e.g., "https://dev.nextgencoin.site/keycloak/auth/realms/dasp-user")
            audience: Expected audience claim (optional)
        """
        self.issuer = issuer
        self.audience = audience
        
        # Construct JWKS URL for Keycloak (standard path)
        if "/realms/" in issuer:
            self.jwks_url = f"{issuer}/protocol/openid-connect/certs"
        else:
            # For other providers, you might need to discover this
            self.jwks_url = f"{issuer}/.well-known/jwks.json"
            
        # Initialize JWKS client for key retrieval
        self.jwks_client = PyJWKClient(self.jwks_url)
        
        # Allowed algorithms (be explicit about what you accept)
        self.allowed_algorithms = jwks_algorithms
    
    def validate_token(self, token: str, options: Optional[Dict[str, bool]] = None) -> Dict[str, Any]:
        """
        Validate JWT token and return decoded payload
        """
        try:
            # Get the signing key from JWKS
            signing_key = self._get_signing_key(token)
            
            # Define default validation options
            if options is None:
                options = {
                    'verify_signature': True,
                    'verify_exp': False,
                    'verify_iat': True,
                    'verify_iss': True,
                    'verify_aud': self.audience is not None,
                    'require_exp': True,
                    'require_iat': True,
                    'require_iss': True,
                }
            
            # Decode and validate the token
            payload = jwt.decode(
                token,
                signing_key.key,
                algorithms=self.allowed_algorithms,
                issuer=self.issuer,
                audience=self.audience,
                options=options
            )
            
            # Additional custom validations
            self._validate_custom_claims(payload)
            
            logging.info(f"Token validated successfully for user: {payload.get('email', 'unknown')}")
            return payload
            
        except jwt.ExpiredSignatureError:
            logging.error("Token has expired")
            raise
        except jwt.InvalidIssuerError:
            logging.error(f"Invalid issuer. Expected: {self.issuer}")
            raise
        except jwt.InvalidAudienceError:
            logging.error(f"Invalid audience. Expected: {self.audience}")
            raise
        except jwt.InvalidTokenError as e:
            logging.error(f"Token validation failed: {str(e)}")
            raise
        except Exception as e:
            logging.error(f"Unexpected error during token validation: {str(e)}")
            raise jwt.InvalidTokenError(f"Token validation failed: {str(e)}")
    
    def _get_signing_key(self, token: str):
        """Get the signing key from JWKS endpoint"""
        try:
            return self.jwks_client.get_signing_key_from_jwt(token)
        except Exception as e:
            logging.error(f"Failed to get signing key: {str(e)}")
            raise jwt.InvalidTokenError("Unable to retrieve signing key")
    
    def _validate_custom_claims(self, payload: Dict[str, Any]):
        """
        Validate custom claims specific to your application
        Adjust this method based on your requirements
        """
        # Check token type
        if payload.get("typ") != "Bearer":
            raise jwt.InvalidTokenError("Invalid token type")
        
        # Validate issued at time (shouldn't be too far in the future)
        iat = payload.get("iat")
        if iat:
            now = datetime.now(timezone.utc)
            
            # Allow 5 minutes clock skew
            if iat > now.timestamp() + 300:
                raise jwt.InvalidTokenError("Token issued too far in the future")
        
        # Validate required custom claims for your application
        required_claims = ["sub", "email", "partnerId"]
        for claim in required_claims:
            if not payload.get(claim):
                raise jwt.InvalidTokenError(f"Missing required claim: {claim}")

# Usage example
def example_usage():
    """Example of how to use the JWT validator"""
    
    # Your JWT token
    token = "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJZUnRDeUZ0UTFGLXE0ci1STnJYLWYzQ2o0OVQ4aWMyeGxHVXU3NFNOYzA0In0.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Fl36inmDOACEUdl6DsimTTg4o1GdNZgpCJIkiCsYLydU60MoHRdVv68S3NTewd93dDgbWzNMiIb8RSUza8K34q6jZMTc4gAzNaOoeS-pY2LNplrdJS79tUgOhx2G20Na1FKYMYg5amPzcMSMB1-ko9UP_RCoCuScP20EVEz-4G-8frcktYL8LQgKH7RI_rg4hW29Ak2rLGdP8wjAV5whUFVd4_znHq9CI_3Oh4WZ0Y3NZkI_Ncw1qfZN5LBv-9J1Xn6aRG0-5M86yI4ysAse1x0p0lhJyl5BlWztFXCZQThVx6V2Hv8rpR5bzXxhpjpfGncmyD7mnsVIiKxy44mcCg"    
    # Initialize validator
    validator = JWTValidator(
        issuer="https://dev.nextgencoin.site/keycloak/auth/realms/dasp-user",
        audience=None,
        jwks_algorithms=["RS256", "RS512", "ES256"]
    )
    
    try:
        # Validate the token
        options = {
                    'verify_signature': True,
                    'verify_exp': False,
                    'verify_iat': True,
                    'verify_iss': True,
                    'verify_aud': False,
                    'require_exp': True,
                    'require_iat': True,
                    'require_iss': True,
        }
        payload = validator.validate_token(token,options)
        username = payload["username"]
        partner_id = payload["partnerId"]
        print(f"User {username} authenticated. Client ID: {partner_id}")
        return payload
        
    except jwt.ExpiredSignatureError:
        print("Token has expired")
        return None
    except jwt.InvalidTokenError as e:
        print(f"Token validation failed: {e}")
        return None


if __name__ == "__main__":
    # Example usage
    example_usage()
