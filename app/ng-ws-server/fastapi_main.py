#!/usr/bin/env python3
"""
FastAPI main entry point with WebSocket server integration.

This module creates a FastAPI application that runs alongside the WebSocket server,
providing REST API endpoints for health checks, statistics, trade execution, and withdrawals.
"""

from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, PlainTextResponse
import os
from pathlib import Path

import asyncio
import logging
import signal
import sys
import time
from contextlib import asynccontextmanager
from typing import Dict, Any

import json
import decimal
import aiohttp
import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

# Import WebSocket server and configuration
from server import WebSocketServer
from config import get_config
from services.shared_state import initialize_shared_state, cleanup_shared_state, get_shared_state

# Import API routers
from api.health import router as health_router
from api.stats import router as stats_router
from api.trading import router as trading_router
from api.withdrawals import router as withdrawals_router

# Import withdrawal service initialization
from services.withdrawal_dynamodb_service import initialize_withdrawal_dynamodb_service, get_withdrawal_dynamodb_service

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global WebSocket server instance
websocket_server: WebSocketServer = None

# Global background task for webhook retry monitoring
webhook_retry_task: asyncio.Task = None

class DecimalEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, decimal.Decimal):
            return int(obj) if obj % 1 == 0 else float(obj)
        return super().default(obj)
    
async def retry_webhook_call(webhook_url: str, client_withdrawal_id: str, withdrawal_data: Dict[str, Any],
                           dynamodb_service) -> int:
    """
    Retry a webhook call and handle DynamoDB updates based on response.
    This is similar to the _call_webhook method in withdrawal_service.py but adapted for retry monitoring.

    Args:
        webhook_url: Webhook URL to call
        client_withdrawal_id: Client withdrawal ID for DynamoDB operations
        withdrawal_data: Data to send to webhook
        dynamodb_service: DynamoDB service instance for updates

    Returns:
        HTTP status code from webhook call
    """
    try:
        timeout = aiohttp.ClientTimeout(total=30)  # 30 second timeout
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.post(webhook_url, json=withdrawal_data) as resp:
                status_code = resp.status
                logger.info(f"Webhook retry response for {client_withdrawal_id}: {status_code}")

                if status_code == 200:
                    # Successful webhook call - remove from DynamoDB and DataFrame
                    success = await dynamodb_service.remove_withdrawal_request(client_withdrawal_id)
                    if success:
                        logger.info(f"Webhook retry successful - removed {client_withdrawal_id} from tracking")
                    else:
                        logger.error(f"Failed to remove {client_withdrawal_id} from DynamoDB after successful retry")
                else:
                    # Failed webhook call - update DynamoDB with failure info
                    error_text = await resp.text()
                    success = await dynamodb_service.update_webhook_failure(
                        client_withdrawal_id,
                        withdrawal_data,
                        status_code,
                        f"Retry failed - HTTP {status_code}: {error_text[:500]}"
                    )
                    if success:
                        logger.info(f"Updated DynamoDB with webhook retry failure for {client_withdrawal_id}")
                    else:
                        logger.error(f"Failed to update DynamoDB with webhook retry failure for {client_withdrawal_id}")

                return status_code

    except asyncio.TimeoutError:
        logger.error(f"Webhook retry timeout for {client_withdrawal_id}")
        await dynamodb_service.update_webhook_failure(
            client_withdrawal_id,
            withdrawal_data,
            408,
            "Retry failed - Request timeout"
        )
        return 408
    except Exception as e:
        logger.error(f"Error during webhook retry for {client_withdrawal_id}: {e}")
        # Update DynamoDB with exception info
        await dynamodb_service.update_webhook_failure(
            client_withdrawal_id,
            withdrawal_data,
            500,
            f"Retry failed - Exception: {str(e)[:500]}"
        )
        return 500


async def process_webhook_retries(ready_for_retry_df, dynamodb_service) -> None:
    """
    Process webhook retries for all webhooks that are ready for retry.

    Args:
        ready_for_retry_df: DataFrame containing webhooks ready for retry
        dynamodb_service: DynamoDB service instance
    """
    if ready_for_retry_df is None or ready_for_retry_df.empty:
        return

    retry_count = 0
    success_count = 0
    failure_count = 0

    logger.info(f"Starting webhook retry processing for {len(ready_for_retry_df)} webhooks")

    for index, row in ready_for_retry_df.iterrows():
        try:
            client_withdrawal_id = row['client_withdrawal_id']
            webhook_url = row.get('callback_webhook_url')
        
            if not webhook_url:
                logger.warning(f"No webhook URL found for {client_withdrawal_id}, skipping retry")
                continue

            logger.info(f"Retrying webhook for {client_withdrawal_id} (attempt #{row.get('webhook_attempts')})")
            #logger.info(f"Webhook webhook_call_data: {row.get('webhook_call_data')}")
            webhook_payload = json.loads(json.dumps(row.get('webhook_call_data'), cls=DecimalEncoder))
            #logger.info(f"Webhook webhook_call_data: {webhook_payload}")
            # Perform the webhook retry
            status_code = await retry_webhook_call(
                webhook_url,
                client_withdrawal_id,
                webhook_payload,
                dynamodb_service
            )

            retry_count += 1

            if status_code == 200:
                success_count += 1
                logger.info(f"Webhook retry successful for {client_withdrawal_id}")
            else:
                failure_count += 1
                logger.warning(f"Webhook retry failed for {client_withdrawal_id} with status {status_code}")

            # Add a small delay between retries to avoid overwhelming the webhook endpoints
            await asyncio.sleep(1)

        except Exception as e:
            logger.error(f"Error processing webhook retry for {client_withdrawal_id}: {e}")
            failure_count += 1
            continue

    logger.info(f"Webhook retry processing completed: {retry_count} retries attempted, "
               f"{success_count} successful, {failure_count} failed")


async def monitor_failed_webhooks():
    """
    Background task that monitors failed webhooks and processes retries every 2 minutes.
    Similar to the WebSocket idle connection monitoring service.
    """
    logger.info("Starting failed webhooks retry monitor task (check interval: 2 minutes)")

    while True:
        try:
            # Get the DynamoDB service
            dynamodb_service = get_withdrawal_dynamodb_service()

            if dynamodb_service is None:
                logger.warning("DynamoDB service not available, skipping webhook retry check")
                await asyncio.sleep(120)  # Wait 2 minutes before next check
                continue

            # Get webhooks ready for retry
            ready_for_retry_df = dynamodb_service.get_failed_webhooks_ready_for_retry()

            if ready_for_retry_df is None or ready_for_retry_df.empty:
                logger.debug("No failed webhooks ready for retry at this time")
            else:
                webhook_count = len(ready_for_retry_df)
                logger.info(f"Found {webhook_count} failed webhooks ready for retry")

                # Log summary of webhooks ready for retry
                if 'client_withdrawal_id' in ready_for_retry_df.columns:
                    withdrawal_ids = ready_for_retry_df['client_withdrawal_id'].tolist()
                    logger.info(f"Withdrawal IDs ready for retry: {withdrawal_ids[:10]}{'...' if len(withdrawal_ids) > 10 else ''}")

                # Process webhook retries
                await process_webhook_retries(ready_for_retry_df, dynamodb_service)

            # Wait for 2 minutes before the next check
            await asyncio.sleep(120)

        except asyncio.CancelledError:
            logger.info("Failed webhooks retry monitor task cancelled")
            break
        except Exception as e:
            logger.error(f"Error in failed webhooks retry monitor: {str(e)}")
            # Continue running despite errors
            await asyncio.sleep(120)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    FastAPI lifespan context manager.

    Handles startup and shutdown of the WebSocket server alongside FastAPI.
    """
    global websocket_server, webhook_retry_task

    # Startup
    logger.info("Starting FastAPI application with WebSocket server...")

    try:
        # Create and initialize WebSocket server
        websocket_server = WebSocketServer()

        # Initialize shared state
        initialize_shared_state(websocket_server)

        # Initialize WebSocket server
        if not await websocket_server.initialize():
            logger.error("Failed to initialize WebSocket server")
            raise RuntimeError("WebSocket server initialization failed")

        # Start WebSocket server in background
        asyncio.create_task(websocket_server.start())

        # Wait a moment for the server to start
        await asyncio.sleep(1)

        # Initialize withdrawal DynamoDB service and collect failed webhooks
        logger.info("Initializing withdrawal DynamoDB service...")
        await initialize_withdrawal_dynamodb_service()
        logger.info("Withdrawal DynamoDB service initialized successfully")

        # Start the failed webhooks retry monitoring task
        logger.info("Starting failed webhooks retry monitoring task...")
        webhook_retry_task = asyncio.create_task(monitor_failed_webhooks())
        logger.info("Failed webhooks retry monitoring task started successfully")

        logger.info("FastAPI application and WebSocket server started successfully")

        yield
        
    except Exception as e:
        logger.error(f"Error during startup: {e}")
        raise
    
    # Shutdown
    logger.info("Shutting down FastAPI application and WebSocket server...")

    try:
        # Cancel the webhook retry monitoring task
        if webhook_retry_task and not webhook_retry_task.done():
            logger.info("Cancelling failed webhooks retry monitoring task...")
            webhook_retry_task.cancel()
            try:
                await webhook_retry_task
            except asyncio.CancelledError:
                logger.info("Failed webhooks retry monitoring task cancelled successfully")

        if websocket_server:
            await websocket_server.stop()

        cleanup_shared_state()

        logger.info("Shutdown completed successfully")

    except Exception as e:
        logger.error(f"Error during shutdown: {e}")


# Create FastAPI application
app = FastAPI(
    title="NextGen Trading Server",
    description="FastAPI application with WebSocket server for crypto trading",
    version="1.0.0",
    lifespan=lifespan
)

# Determine the static files directory
# Assuming the HTML file is in the same directory as fastapi_main.py
static_dir = Path(__file__).parent
html_file = static_dir / "nextgen_websocket_client.html"

# Mount static files directory
app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routers
app.include_router(health_router)
app.include_router(stats_router)
app.include_router(trading_router)
app.include_router(withdrawals_router)


@app.get("/test", response_class=PlainTextResponse)
async def test_endpoint():
    """
    Simple test endpoint to verify the server is responding.
    """
    return "Server is running correctly"


@app.get("/client", response_class=HTMLResponse)
async def get_websocket_client():
    """
    Serve the WebSocket client HTML interface.

    Returns:
        HTMLResponse: The WebSocket client HTML page
    """
    try:
        # Check if the file exists
        if not os.path.exists(html_file):
            logger.error(f"WebSocket client HTML file not found at {html_file}")
            return HTMLResponse(
                content=f"<html><body><h1>Error: HTML file not found</h1><p>The file {html_file} does not exist.</p></body></html>",
                status_code=404
            )

        # Try to read the file
        try:
            with open(html_file, "r") as f:
                content = f.read()
        except Exception as e:
            logger.error(f"Error reading HTML file: {e}")
            return HTMLResponse(
                content=f"<html><body><h1>Error reading HTML file</h1><p>{str(e)}</p></body></html>",
                status_code=500
            )

        # Get configuration
        config = get_config()
        ws_url = f"ws://{config.host}:{config.ws_port}/ws"

        # Replace the WebSocket URL in the HTML content
        content = content.replace(
            'value="http://localhost:8766/ws"',
            f'value="{ws_url}"'
        )

        logger.info(f"Successfully serving WebSocket client HTML from {html_file}")
        return HTMLResponse(content=content)

    except Exception as e:
        logger.error(f"Unexpected error serving WebSocket client: {e}", exc_info=True)
        return HTMLResponse(
            content=f"<html><body><h1>Server Error</h1><p>{str(e)}</p></body></html>",
            status_code=500
        )


@app.get("/")
async def root() -> Dict[str, Any]:
    """
    Root endpoint providing basic API information.
    
    Returns:
        Dict: Basic API information
    """
    shared_state = get_shared_state()
    
    return {
        "service": "NextGen Trading Server",
        "version": "1.0.0",
        "status": "running",
        "timestamp": time.time(),
        "uptime_seconds": shared_state.get_uptime_seconds(),
        "websocket_server_initialized": shared_state.is_initialized(),
        "endpoints": {
            "health": "/health/",
            "trading": "/trading/",
            "withdrawals": "/withdrawals/",
            "docs": "/docs",
            "redoc": "/redoc"
        }
    }


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """
    Global exception handler for unhandled exceptions.
    
    Args:
        request: The request that caused the exception
        exc: The exception that was raised
        
    Returns:
        JSONResponse: Error response
    """
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal Server Error",
            "message": "An unexpected error occurred",
            "timestamp": time.time()
        }
    )


async def shutdown_handler():
    """Handle graceful shutdown."""
    global websocket_server, webhook_retry_task

    logger.info("Received shutdown signal")

    # Cancel the webhook retry monitoring task first
    if webhook_retry_task and not webhook_retry_task.done():
        logger.info("Cancelling failed webhooks retry monitoring task...")
        webhook_retry_task.cancel()

    if websocket_server:
        await websocket_server.stop()

    # Cancel all running tasks
    tasks = [t for t in asyncio.all_tasks() if t is not asyncio.current_task()]
    for task in tasks:
        task.cancel()

    await asyncio.gather(*tasks, return_exceptions=True)


def setup_signal_handlers():
    """Set up signal handlers for graceful shutdown."""
    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}")
        asyncio.create_task(shutdown_handler())
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


async def main():
    """
    Main entry point for running the FastAPI application.
    """
    try:
        config = get_config()
        logger.info(f"REST API port from config: {config.rest_port}")

        # Check if the WebSocket client HTML file exists
        if not os.path.exists(html_file):
            logger.warning(
                f"WebSocket client HTML file not found at {html_file}. The client interface will not be available.")
        
        # Set up signal handlers
        setup_signal_handlers()
        
        # Configure uvicorn
        uvicorn_config = uvicorn.Config(
            app,
            host=config.host,
            port=config.rest_port,
            log_level="info",
            access_log=True,
            reload=False  # Set to True for development
        )
        
        server = uvicorn.Server(uvicorn_config)
        
        logger.info(f"Starting FastAPI server on {config.host}:{config.rest_port}")
        logger.info(f"WebSocket server will run on {config.host}:{config.ws_port}")
        logger.info("API documentation available at /docs")
        
        await server.serve()
        
    except Exception as e:
        logger.error(f"Error starting FastAPI application: {e}")
        sys.exit(1)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
    except Exception as e:
        logger.error(f"Unhandled exception in main: {e}")
        sys.exit(1)
