"""
DynamoDB service for withdrawal request persistence.

This service handles:
- Storing successful withdrawal requests in DynamoDB for persistence
- Removing records upon successful webhook calls (response 200)
- Updating records with failure information when webhook calls fail
- Providing query capabilities for withdrawal records
"""

import decimal
import logging
import boto3
import asyncio
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, List
from decimal import Decimal
import json
import pandas as pd

from botocore.exceptions import ClientError, BotoCoreError
from models.api_models import WithdrawalRequest

# Import Vault client
from hcvault import get_vault_client

# Configure logging
logger = logging.getLogger(__name__)

vault_path = "kv/data/apps/trading-if"

class DecimalEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, decimal.Decimal):
            return int(obj) if obj % 1 == 0 else float(obj)
        return super().default(obj)
    
class WithdrawalDynamoDBService:
    """
    DynamoDB service for managing withdrawal request persistence.
    """
    def __init__(self):
        # Initialize Vault and load secrets
        vclient = get_vault_client()
        secrets = vclient.get_secret(vault_path)

        self.table_name = secrets.get("AWS_DYNAMODB_TABLE_NAME")
        self.region_name = secrets.get("AWS_DYNAMODB_TABLE_REGION")
        self.max_webhook_retry_backoff_minutes = secrets.get("MAX_WEBHOOK_RETRY_BACKOFF_MINUTES", 240)
        self.max_webhook_retry_count = secrets.get("MAX_WEBHOOK_RETRY_COUNT", 5)

        # DataFrame to store failed webhooks for processing
        self.failed_webhooks_df: Optional[pd.DataFrame] = None

        try:
            # Initialize DynamoDB resource
            self.dynamodb = boto3.resource(
                'dynamodb',
                region_name=self.region_name,
                aws_access_key_id=secrets.get("AWS_ACCESS_KEY_ID"),
                aws_secret_access_key=secrets.get("AWS_SECRET_ACCESS_KEY")
            )

            self.table = self.dynamodb.Table(self.table_name)

            logger.info(f"DynamoDB service initialized for table: {self.table_name}")

        except Exception as e:
            logger.error(f"Failed to initialize DynamoDB service: {e}")
            raise
    
    async def store_withdrawal_request(self, request: WithdrawalRequest) -> bool:
        """
        Store a successful withdrawal request in DynamoDB.
        """
        try:
            # Prepare the item for DynamoDB
            item = {
                'client_withdrawal_id': request.client_withdrawal_id,  # Primary key
                'request_id': request.request_id,
                'quantity': request.Quantity,
                'currency': request.Currency,
                'market_account': request.MarketAccount,
                'withdrawal_type': request.withdrawal_type.value,
                'destination_address': request.RoutingInfo.DestinationAddress,
                'blockchain_network': request.RoutingInfo.BlockchainNetwork or '',
                'memo': request.RoutingInfo.Memo or '',
                'callback_webhook_url': request.callback_webhook_url,
                'created_timestamp': datetime.now(timezone.utc).isoformat(),
                'status': 'pending',  # Initial status
                'webhook_attempts': 0,
                'last_webhook_attempt': None,
                'last_webhook_response_code': None,
                'last_webhook_error': None,
                'next_attempt_timestamp': None
            }
            
            # Store in DynamoDB
            response = self.table.put_item(Item=item)
            
            logger.info(f"Withdrawal request stored in DynamoDB: {request.client_withdrawal_id}")
            return True
            
        except ClientError as e:
            logger.error(f"DynamoDB ClientError storing withdrawal request {request.client_withdrawal_id}: {e}")
            return False
        except Exception as e:
            logger.error(f"Error storing withdrawal request {request.client_withdrawal_id}: {e}")
            return False
    
    async def get_withdrawal_request(self, client_withdrawal_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve a withdrawal request from DynamoDB.
        """
        try:
            response = self.table.get_item(
                Key={'client_withdrawal_id': client_withdrawal_id}
            )
            
            if 'Item' in response:
                logger.debug(f"Retrieved withdrawal request: {client_withdrawal_id}")
                return response['Item']
            else:
                logger.warning(f"Withdrawal request not found: {client_withdrawal_id}")
                return None
                
        except ClientError as e:
            logger.error(f"DynamoDB ClientError retrieving withdrawal request {client_withdrawal_id}: {e}")
            return None
        except Exception as e:
            logger.error(f"Error retrieving withdrawal request {client_withdrawal_id}: {e}")
            return None
    
    async def remove_withdrawal_request(self, client_withdrawal_id: str) -> bool:
        """
        Remove a withdrawal request from DynamoDB after successful webhook call.
        This is called when webhook returns status code 200.
        Also removes the record from the pandas DataFrame if it exists.
        """
        try:
            response = self.table.delete_item(
                Key={'client_withdrawal_id': client_withdrawal_id},
                ReturnValues='ALL_OLD'
            )

            if 'Attributes' in response:
                # Remove from pandas DataFrame as well
                self._remove_from_dataframe(client_withdrawal_id)

                logger.info(f"Withdrawal request removed from DynamoDB and DataFrame: {client_withdrawal_id}")
                return True
            else:
                logger.warning(f"Withdrawal request not found for removal: {client_withdrawal_id}")
                return False

        except ClientError as e:
            logger.error(f"DynamoDB ClientError removing withdrawal request {client_withdrawal_id}: {e}")
            return False
        except Exception as e:
            logger.error(f"Error removing withdrawal request {client_withdrawal_id}: {e}")
            return False
    
    async def update_webhook_failure(self, client_withdrawal_id: str, webhook_call_data: Dict[str, Any],
                                   response_code: int,
                                   error_message: Optional[str] = None) -> bool:
        """
        Update withdrawal request with webhook failure information.
        This is called when webhook call fails (non-200 response).
        Also updates the pandas DataFrame if it exists.
        """
        try:
            # Get current attempt count
            current_item = await self.get_withdrawal_request(client_withdrawal_id)
            if not current_item:
                logger.error(f"Cannot update webhook failure - withdrawal request not found: {client_withdrawal_id}")
                return False

            current_attempts = int(current_item.get('webhook_attempts', 0))
            new_attempts = current_attempts + 1

            # Calculate next attempt time with exponential backoff
            # Base delay: 2 minutes, doubles with each attempt, max 4 hours
            backoff_minutes = min(2 * (2 ** (new_attempts - 1)), int(self.max_webhook_retry_backoff_minutes))
            next_attempt_time = datetime.now(timezone.utc) + timedelta(minutes=backoff_minutes)
            current_timestamp = datetime.now(timezone.utc).isoformat()

            # Update the item in DynamoDB
            response = self.table.update_item(
                Key={'client_withdrawal_id': client_withdrawal_id},
                UpdateExpression="""
                    SET webhook_attempts = :attempts,
                        last_webhook_attempt = :timestamp,
                        last_webhook_response_code = :response_code,
                        last_webhook_error = :error_msg,
                        webhook_call_data = :webhook_call_data,
                        next_attempt_timestamp = :next_attempt,
                        #status = :status
                """,
                ExpressionAttributeNames={
                    '#status': 'status'  # 'status' is a reserved word in DynamoDB
                },
                ExpressionAttributeValues={
                    ':attempts': new_attempts,
                    ':timestamp': current_timestamp,
                    ':response_code': response_code,
                    ':error_msg': error_message or '',
                    ':webhook_call_data': webhook_call_data,
                    ':next_attempt': next_attempt_time.isoformat(),
                    ':status': 'webhook_failed'
                },
                ReturnValues='ALL_NEW'
            )

            # Update the pandas DataFrame if it exists
            self._update_dataframe_webhook_failure_new(
                client_withdrawal_id,
                response,
                next_attempt_time.isoformat()
            )

            logger.info(f"Updated webhook failure for withdrawal {client_withdrawal_id}: "
                       f"attempt {new_attempts}, response_code {response_code}")
            return True

        except ClientError as e:
            logger.error(f"DynamoDB ClientError updating webhook failure {client_withdrawal_id}: {e}")
            return False
        except Exception as e:
            logger.error(f"Error updating webhook failure {client_withdrawal_id}: {e}")
            return False

    def _update_dataframe_webhook_failure_new(self, client_withdrawal_id: str, dynamodb_response: Dict[str, Any],
                                            next_attempt_timestamp: str) -> None:
        """
        Update the pandas DataFrame with webhook failure information.
        This keeps the DataFrame in sync with DynamoDB updates.
        If the record doesn't exist in the DataFrame, it will be added.
        """
        logger.info(f"Updating DataFrame for webhook failure {client_withdrawal_id}")
        #logger.info(f"DynamoDB response: {dynamodb_response}")
        logger.info(f"Next attempt timestamp: {next_attempt_timestamp}")    

        dynamodb_response_dict = json.loads(json.dumps(dynamodb_response, cls=DecimalEncoder))

        #logger.info(f"DynamoDB response dict: {dynamodb_response_dict}") 

        if self.failed_webhooks_df is None:
            logger.info(f"DataFrame not initialized, skipping DataFrame update for {client_withdrawal_id}")
            return
        try:
            bNewRecordRequired = False
            if self.failed_webhooks_df.empty:
                bNewRecordRequired = True
            
            # Check if the record already exists in the DataFrame
            if not bNewRecordRequired:
                logger.info('... 1')
                mask = self.failed_webhooks_df['client_withdrawal_id'] == client_withdrawal_id
                logger.info('... 2')
                
                if mask.any():
                    logger.info(f"Existing DataFrame record for {client_withdrawal_id}")
                    self.failed_webhooks_df.loc[mask, 'webhook_attempts'] = dynamodb_response_dict['Attributes']['webhook_attempts']
                    self.failed_webhooks_df.loc[mask, 'last_webhook_attempt'] = pd.to_datetime(dynamodb_response_dict['Attributes']['last_webhook_attempt'])
                    self.failed_webhooks_df.loc[mask, 'last_webhook_response_code'] = dynamodb_response_dict['Attributes']['last_webhook_response_code']
                    self.failed_webhooks_df.loc[mask, 'last_webhook_error'] = dynamodb_response_dict['Attributes']['last_webhook_error']
                    self.failed_webhooks_df.loc[mask, 'next_attempt_timestamp'] = pd.to_datetime(next_attempt_timestamp)
                else:
                    bNewRecordRequired = True
            
            if bNewRecordRequired:
                logger.info(f"New DataFrame record for {client_withdrawal_id}")
                #logger.info(f"New record: {dynamodb_response_dict['Attributes']}")
                # Create a new record with the available data
                new_record = {
                    'client_withdrawal_id': client_withdrawal_id,
                    'blockchain_network': dynamodb_response_dict['Attributes']['blockchain_network'],
                    'callback_webhook_url': dynamodb_response_dict['Attributes']['callback_webhook_url'],
                    'created_timestamp': pd.to_datetime(dynamodb_response_dict['Attributes']['created_timestamp']),
                    'currency': dynamodb_response_dict['Attributes']['currency'],
                    'destination_address': dynamodb_response_dict['Attributes']['destination_address'],
                    'memo': dynamodb_response_dict['Attributes']['memo'],
                    'market_account': dynamodb_response_dict['Attributes']['market_account'],
                    'quantity': dynamodb_response_dict['Attributes']['quantity'],
                    'request_id': dynamodb_response_dict['Attributes']['request_id'],
                    'withdrawal_type': dynamodb_response_dict['Attributes']['withdrawal_type'],
                    'webhook_attempts': dynamodb_response_dict['Attributes']['webhook_attempts'],
                    'last_webhook_attempt': pd.to_datetime(dynamodb_response_dict['Attributes']['last_webhook_attempt']),
                    'last_webhook_response_code': dynamodb_response_dict['Attributes']['last_webhook_response_code'],
                    'last_webhook_error': dynamodb_response_dict['Attributes']['last_webhook_error'],
                    'next_attempt_timestamp': pd.to_datetime(next_attempt_timestamp),
                    'webhook_call_data': dynamodb_response_dict['Attributes']['webhook_call_data'],
                    'status': 'webhook_failed'
                }
                new_record_df = pd.DataFrame([new_record])
                # Create a new DataFrame with the record and concatenate with existing DataFrame
                #new_df = pd.DataFrame([dynamodb_response_dict['Attributes']])

                self.failed_webhooks_df = pd.concat([self.failed_webhooks_df, new_record_df], ignore_index=True)
                
                logger.info(f"Added new record to DataFrame for {client_withdrawal_id}")
                
                # Re-sort the DataFrame by last webhook attempt (most recent first)
                if 'next_attempt_timestamp' in self.failed_webhooks_df.columns:
                    self.failed_webhooks_df = self.failed_webhooks_df.sort_values(
                        'next_attempt_timestamp',
                        ascending=True
                    )
        except Exception as e:
            logger.error(f"Error updating DataFrame for webhook failure {client_withdrawal_id}: {e}")
            # Don't raise the exception - DataFrame update failure shouldn't break the main flow


    def _remove_from_dataframe(self, client_withdrawal_id: str) -> None:
        """
        Remove a withdrawal record from the pandas DataFrame.
        This is called when a webhook succeeds and the record is removed from DynamoDB.
        """
        if self.failed_webhooks_df is None or self.failed_webhooks_df.empty:
            logger.debug(f"DataFrame not initialized or empty, skipping DataFrame removal for {client_withdrawal_id}")
            return

        try:
            # Check if the withdrawal ID exists in the DataFrame
            mask = self.failed_webhooks_df['client_withdrawal_id'] == client_withdrawal_id

            if mask.any():
                # Remove the record from DataFrame
                self.failed_webhooks_df = self.failed_webhooks_df[~mask].copy()
                logger.debug(f"Removed {client_withdrawal_id} from DataFrame (successful webhook)")
            else:
                logger.debug(f"Withdrawal ID {client_withdrawal_id} not found in DataFrame for removal")

        except Exception as e:
            logger.error(f"Error removing {client_withdrawal_id} from DataFrame: {e}")
            # Don't raise the exception - DataFrame update failure shouldn't break the main flow

    async def list_failed_webhooks(self, limit: int = 100) -> List[Dict[str, Any]]:
        """
        List withdrawal requests with failed webhook calls.
        This can be used for monitoring and retry mechanisms.
        """
        try:
            response = self.table.scan(
                FilterExpression='#status = :status',
                ExpressionAttributeNames={
                    '#status': 'status'
                },
                ExpressionAttributeValues={
                    ':status': 'webhook_failed'
                },
                Limit=limit
            )
            items = response['Items']

            # Handle pagination
            while 'LastEvaluatedKey' in response:
                response = self.table.scan(
                                ExclusiveStartKey=response['LastEvaluatedKey'],
                                FilterExpression='#status = :status',
                                ExpressionAttributeNames={
                                    '#status': 'status'
                                },
                                ExpressionAttributeValues={
                                    ':status': 'webhook_failed'
                                },
                                Limit=limit
                            )
                items.extend(response['Items'])

            logger.debug(f"Retrieved {len(items)} withdrawal requests with failed webhooks")
            return items
            
        except ClientError as e:
            logger.error(f"DynamoDB ClientError listing failed webhooks: {e}")
            return []
        except Exception as e:
            logger.error(f"Error listing failed webhooks: {e}")
            return []

    async def collect_failed_webhooks_on_startup(self) -> bool:
        """
        Collect all failed webhooks on service startup and store them as pandas DataFrame.
        This method should be called during service initialization.

        Returns:
            bool: True if collection was successful, False otherwise
        """
        try:
            logger.info("Collecting failed webhooks on startup...")

            # Get all failed webhooks
            failed_webhooks = await self.list_failed_webhooks(limit=1000)  # Increase limit for startup collection

            if not failed_webhooks:
                logger.info("No failed webhooks found on startup")
                self.failed_webhooks_df = pd.DataFrame()
                return True

            # Convert to pandas DataFrame
            self.failed_webhooks_df = pd.DataFrame(failed_webhooks)

            # Convert timestamp columns to datetime for better processing
            timestamp_columns = [
                'created_timestamp',
                'last_webhook_attempt',
                'next_attempt_timestamp'
            ]

            for col in timestamp_columns:
                if col in self.failed_webhooks_df.columns:
                    self.failed_webhooks_df[col] = pd.to_datetime(
                        self.failed_webhooks_df[col],
                        errors='coerce'
                    )

            # Sort by last webhook attempt (most recent first)
            if 'next_attempt_timestamp' in self.failed_webhooks_df.columns:
                self.failed_webhooks_df = self.failed_webhooks_df.sort_values(
                    'next_attempt_timestamp',
                    ascending=True
                )

            logger.info(f"Successfully collected {len(self.failed_webhooks_df)} failed webhooks on startup")
            logger.info(f"Failed webhooks DataFrame shape: {self.failed_webhooks_df.shape}")

            # Log summary statistics
            if not self.failed_webhooks_df.empty:
                logger.info("Failed webhooks summary:")
                logger.info(f"- Total failed webhooks: {len(self.failed_webhooks_df)}")

                if 'webhook_attempts' in self.failed_webhooks_df.columns:
                    logger.info(f"- Average webhook attempts: {self.failed_webhooks_df['webhook_attempts'].mean():.2f}")
                    logger.info(f"- Max webhook attempts: {self.failed_webhooks_df['webhook_attempts'].max()}")

                if 'last_webhook_response_code' in self.failed_webhooks_df.columns:
                    response_codes = self.failed_webhooks_df['last_webhook_response_code'].value_counts()
                    logger.info(f"- Response code distribution: {response_codes.to_dict()}")

                if 'currency' in self.failed_webhooks_df.columns:
                    currency_dist = self.failed_webhooks_df['currency'].value_counts()
                    logger.info(f"- Currency distribution: {currency_dist.to_dict()}")

            return True

        except Exception as e:
            logger.error(f"Error collecting failed webhooks on startup: {e}")
            self.failed_webhooks_df = pd.DataFrame()  # Initialize empty DataFrame on error
            return False

    def get_failed_webhooks_dataframe(self) -> Optional[pd.DataFrame]:
        """
        Get the failed webhooks DataFrame.

        Returns:
            Optional[pd.DataFrame]: The failed webhooks DataFrame, or None if not initialized
        """
        return self.failed_webhooks_df

    async def refresh_failed_webhooks_dataframe(self) -> bool:
        """
        Refresh the failed webhooks DataFrame with current data from DynamoDB.
        This can be called periodically to update the DataFrame.

        Returns:
            bool: True if refresh was successful, False otherwise
        """
        logger.info("Refreshing failed webhooks DataFrame...")
        return await self.collect_failed_webhooks_on_startup()

    def get_failed_webhooks_ready_for_retry(self) -> Optional[pd.DataFrame]:
        """
        Get failed webhooks that are ready for retry based on next_attempt_timestamp.
        This is an example of processing logic that can be built on the DataFrame.

        Returns:
            Optional[pd.DataFrame]: DataFrame with webhooks ready for retry, or None if not initialized
        """
        if self.failed_webhooks_df is None or self.failed_webhooks_df.empty:
            logger.warning("Failed webhooks DataFrame is not available or empty")
            return None

        try:
            current_time = pd.Timestamp.now(tz='UTC')

            # Ensure next_attempt_timestamp is properly converted to datetime
            if 'next_attempt_timestamp' in self.failed_webhooks_df.columns:
                # Make a copy to avoid SettingWithCopyWarning
                df_copy = self.failed_webhooks_df.copy()
                
                # Convert next_attempt_timestamp to datetime if it's not already
                if not pd.api.types.is_datetime64_any_dtype(df_copy['next_attempt_timestamp']):
                    try:
                        df_copy['next_attempt_timestamp'] = pd.to_datetime(
                            df_copy['next_attempt_timestamp'], 
                            errors='coerce'
                        )
                    except Exception as e:
                        logger.warning(f"Error converting next_attempt_timestamp to datetime: {e}")
                
                # Filter webhooks where next_attempt_timestamp is in the past or null
                ready_for_retry = df_copy[
                    (df_copy['next_attempt_timestamp'].isna()) |
                    (df_copy['next_attempt_timestamp'] <= current_time)
                ].copy()
            else:
                # If next_attempt_timestamp column doesn't exist, consider all webhooks ready
                ready_for_retry = self.failed_webhooks_df.copy()

            logger.info(f"Found {len(ready_for_retry)} webhooks ready for retry out of {len(self.failed_webhooks_df)} total failed webhooks")

            return ready_for_retry

        except Exception as e:
            logger.error(f"Error filtering webhooks ready for retry: {e}", exc_info=True)
            return None

    def get_failed_webhooks_summary_stats(self) -> Dict[str, Any]:
        """
        Get summary statistics for failed webhooks.
        This is an example of analytics that can be built on the DataFrame.

        Returns:
            Dict[str, Any]: Summary statistics
        """
        if self.failed_webhooks_df is None or self.failed_webhooks_df.empty:
            return {
                "total_failed_webhooks": 0,
                "message": "No failed webhooks data available"
            }

        try:
            stats = {
                "total_failed_webhooks": len(self.failed_webhooks_df),
                "data_collection_timestamp": pd.Timestamp.now(tz='UTC').isoformat(),
            }

            # Webhook attempts statistics
            if 'webhook_attempts' in self.failed_webhooks_df.columns:
                stats["webhook_attempts"] = {
                    "mean": float(self.failed_webhooks_df['webhook_attempts'].mean()),
                    "median": float(self.failed_webhooks_df['webhook_attempts'].median()),
                    "max": int(self.failed_webhooks_df['webhook_attempts'].max()),
                    "min": int(self.failed_webhooks_df['webhook_attempts'].min())
                }

            # Response code distribution
            if 'last_webhook_response_code' in self.failed_webhooks_df.columns:
                response_codes = self.failed_webhooks_df['last_webhook_response_code'].value_counts()
                stats["response_code_distribution"] = response_codes.to_dict()

            # Currency distribution
            if 'currency' in self.failed_webhooks_df.columns:
                currency_dist = self.failed_webhooks_df['currency'].value_counts()
                stats["currency_distribution"] = currency_dist.to_dict()

            # Time-based analysis
            if 'last_webhook_attempt' in self.failed_webhooks_df.columns:
                last_attempts = self.failed_webhooks_df['last_webhook_attempt'].dropna()
                if not last_attempts.empty:
                    stats["time_analysis"] = {
                        "oldest_failed_attempt": last_attempts.min().isoformat(),
                        "newest_failed_attempt": last_attempts.max().isoformat(),
                        "failed_attempts_last_24h": len(last_attempts[
                            last_attempts >= (pd.Timestamp.now(tz='UTC') - pd.Timedelta(days=1))
                        ])
                    }

            # Ready for retry count
            ready_for_retry = self.get_failed_webhooks_ready_for_retry()
            if ready_for_retry is not None:
                stats["ready_for_retry_count"] = len(ready_for_retry)

            return stats

        except Exception as e:
            logger.error(f"Error generating failed webhooks summary stats: {e}")
            return {
                "error": str(e),
                "total_failed_webhooks": len(self.failed_webhooks_df) if self.failed_webhooks_df is not None else 0
            }


# Singleton instance
_dynamodb_service_instance = None


def get_withdrawal_dynamodb_service() -> WithdrawalDynamoDBService:
    """
    Get the WithdrawalDynamoDBService singleton instance.

    Returns:
        The WithdrawalDynamoDBService instance
    """
    global _dynamodb_service_instance
    if _dynamodb_service_instance is None:
        _dynamodb_service_instance = WithdrawalDynamoDBService()
    return _dynamodb_service_instance


async def initialize_withdrawal_dynamodb_service() -> WithdrawalDynamoDBService:
    """
    Initialize the WithdrawalDynamoDBService singleton instance and collect failed webhooks.
    This should be called during application startup.

    Returns:
        The WithdrawalDynamoDBService instance
    """
    service = get_withdrawal_dynamodb_service()

    # Collect failed webhooks on startup
    await service.collect_failed_webhooks_on_startup()

    return service
