import asyncio
import logging
import os
import socket
from datetime import datetime, timedelta, timezone
from typing import Optional


from kubernetes import client, config
from kubernetes.client.rest import ApiException
from pydantic import BaseModel

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration
LEASE_NAME = os.getenv("LEASE_NAME", "trade-if-service-leader")
NAMESPACE = os.getenv("NAMESPACE", "dasp")
LEASE_DURATION_SECONDS = int(os.getenv("LEASE_DURATION_SECONDS", "600"))  # 10 minutes
RENEW_INTERVAL_SECONDS = int(os.getenv("RENEW_INTERVAL_SECONDS", "600"))  # 10 minutes
POD_NAME = os.getenv("HOSTNAME", socket.gethostname())

class LeaderElection:
    def __init__(self):
        self.is_leader = False
        self.leader_identity = None
        self.coordination_v1 = None
        self.last_renewal = None
        
        # Initialize Kubernetes client
        try:
            # Try in-cluster config first
            config.load_incluster_config()
            logger.info("Loaded in-cluster Kubernetes config")
        except Exception:
            try:
                # Fall back to local kubeconfig
                config.load_kube_config()
                logger.info("Loaded local Kubernetes config")
            except Exception as e:
                logger.error(f"Failed to load Kubernetes config: {e}")
                raise
        
        self.coordination_v1 = client.CoordinationV1Api()
    
    async def try_acquire_leadership(self) -> bool:
        """Attempt to acquire or renew leadership"""
        try:
            current_time = datetime.now(timezone.utc)
            
            # Try to get existing lease
            try:
                lease = self.coordination_v1.read_namespaced_lease(
                    name=LEASE_NAME,
                    namespace=NAMESPACE
                )
                logger.debug(f"Found existing lease: {lease.spec.holder_identity}")
                
                # Check if lease is expired or we are the current holder
                if lease.spec.holder_identity == POD_NAME:
                    # We are already the leader, renew the lease
                    lease.spec.renew_time = current_time
                    lease.spec.lease_duration_seconds = LEASE_DURATION_SECONDS
                    
                    self.coordination_v1.patch_namespaced_lease(
                        name=LEASE_NAME,
                        namespace=NAMESPACE,
                        body=lease
                    )
                    
                    if not self.is_leader:
                        logger.info(f"Renewed leadership for {POD_NAME}")
                        self.is_leader = True
                        self.leader_identity = POD_NAME
                    
                    self.last_renewal = current_time
                    return True
                
                else:
                    # Check if current lease is expired
                    lease_expiry = lease.spec.renew_time + timedelta(seconds=lease.spec.lease_duration_seconds)
                    
                    if current_time > lease_expiry:
                        logger.info(f"Lease expired, attempting to acquire from {lease.spec.holder_identity}")
                        # Lease is expired, try to acquire it
                        lease.spec.holder_identity = POD_NAME
                        lease.spec.renew_time = current_time
                        lease.spec.lease_duration_seconds = LEASE_DURATION_SECONDS
                        lease.spec.acquire_time = current_time
                        lease.spec.lease_transitions = lease.spec.lease_transitions + 1
                        
                        self.coordination_v1.replace_namespaced_lease(
                            name=LEASE_NAME,
                            namespace=NAMESPACE,
                            body=lease
                        )
                        
                        logger.info(f"Acquired leadership for {POD_NAME}")
                        self.is_leader = True
                        self.leader_identity = POD_NAME
                        self.last_renewal = current_time
                        return True
                    else:
                        # Lease is still valid and held by someone else
                        if self.is_leader:
                            logger.info(f"Lost leadership to {lease.spec.holder_identity}")
                            self.is_leader = False
                        
                        self.leader_identity = lease.spec.holder_identity
                        return False
                        
            except ApiException as e:
                if e.status == 404:
                    # Lease doesn't exist, create it
                    logger.info("No existing lease found, creating new lease")
                    
                    lease_body = client.V1Lease(
                        metadata=client.V1ObjectMeta(
                            name=LEASE_NAME,
                            namespace=NAMESPACE
                        ),
                        spec=client.V1LeaseSpec(
                            holder_identity=POD_NAME,
                            lease_duration_seconds=LEASE_DURATION_SECONDS,
                            acquire_time=current_time,
                            renew_time=current_time,
                            lease_transitions=1
                        )
                    )
                    
                    self.coordination_v1.create_namespaced_lease(
                        namespace=NAMESPACE,
                        body=lease_body
                    )
                    
                    logger.info(f"Created lease and acquired leadership for {POD_NAME}")
                    self.is_leader = True
                    self.leader_identity = POD_NAME
                    self.last_renewal = current_time
                    return True
                else:
                    logger.error(f"API error while managing lease: {e}")
                    return False
                    
        except Exception as e:
            logger.error(f"Error in leader election: {e}")
            return False
    
    async def step_down(self):
        """Voluntarily step down from leadership"""
        if self.is_leader:
            try:
                # Update lease to expire immediately
                lease = self.coordination_v1.read_namespaced_lease(
                    name=LEASE_NAME,
                    namespace=NAMESPACE
                )
                
                if lease.spec.holder_identity == POD_NAME:
                    # Set lease to expire in the past
                    lease.spec.renew_time = datetime.now(timezone.utc) - timedelta(seconds=LEASE_DURATION_SECONDS + 1)
                    
                    self.coordination_v1.patch_namespaced_lease(
                        name=LEASE_NAME,
                        namespace=NAMESPACE,
                        body=lease
                    )
                    
                    logger.info(f"Stepped down from leadership: {POD_NAME}")
                
            except Exception as e:
                logger.error(f"Error stepping down: {e}")
            
            self.is_leader = False

