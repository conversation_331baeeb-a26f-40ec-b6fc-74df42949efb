import asyncio
import logging
from time import sleep
import uuid
from datetime import datetime, timezone

from decimal import Decimal, InvalidOperation
from typing import Dict, Any, Optional, Tuple
import json

from scrypt.orders import OrdersClient
from scrypt.rfq import RFQClient
from scrypt.scrypt_websocket_client import ScryptWebsocketClient

from models.api_models import (
    TradeExecutionRequest, 
    TradeExecutionSuccessResponse,
    TradeExecutionErrorResponse,
    ProfitLossInfo
)

logger = logging.getLogger(__name__)


class TradeExecutionService:
    """Service for executing trades with profitability validation."""
    
    def __init__(self):
        # Processing setup/conditions
        self.loss_threshold = Decimal("1000.00") 
        self.quote_timeout = 15  # seconds to wait for quote response
        # Scrypt clients
        self._scrypt_client = ScryptWebsocketClient()
        self._rfq_client = RFQClient(self._scrypt_client)
        self._orders_client = OrdersClient(self._scrypt_client)

        # Futures for awaiting responses
        self.pending_rfqs: Dict[str,asyncio.Future] = {}
        self.pending_orders: Dict[str,asyncio.Future] = {}

        self._scrypt_client.start()
        self._scrypt_session_id = self._scrypt_client.session_id

        sleep(1) # Wait for Scrypt client to start before submitting requests

        now = datetime.now(timezone.utc)
        start_time = now.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        print(f"Using start time for subscriptions: {start_time}")

        self.quote_subscription_id = self._rfq_client.subscribe_quotes(
            start_date=start_time
        )
        self.orders_subscription_id = self._orders_client.subscribe_execution_reports(
             start_date=start_time
        )

        self._orders_client.register_execution_report_callback(self.orders_subscription_id, self._handle_execution_report_msg)
        self._rfq_client.register_quote_callback(self.quote_subscription_id, self._handle_quote_update_msg)

        logger.info("   TradeExecutionService initialized.....")

    async def _await_trade_execution(self, cl_ord_id: str,timeout: float = 30) -> Dict[str, Any]:
        # Await trade execution response
        if cl_ord_id not in self.pending_orders:
            raise ValueError(f"No pending order found for {cl_ord_id}")
        try:
            return await asyncio.wait_for(self.pending_orders[cl_ord_id], timeout=timeout)
        except asyncio.TimeoutError:
            self.pending_orders.pop(cl_ord_id,None)
            raise TimeoutError(f"Trade execution timeout for {cl_ord_id}")
        finally:
            self.pending_orders.pop(cl_ord_id,None)

    async def _handle_execution_report_msg(self, data: Dict[str, Any]):
        #logger.info(f"Received trade data: {json.dumps(data, indent=2)}")
        for td in data.get("data",[]):
            cl_ord_id = td.get("ClOrdID")
            if not cl_ord_id:
                logger.error("Trade data received without ClOrdID")
                continue
            # Resolve pending future
            if cl_ord_id in self.pending_orders and td['OrdStatus']=='Filled':
                self.pending_orders[cl_ord_id].set_result(td)
                logger.info(f"Quote response received for {cl_ord_id}")

    async def _await_rfq_response(self, client_rfq_id: str,timeout: float = 30) -> Dict[str, Any]:
        # Await quote response
        if client_rfq_id not in self.pending_rfqs:
            raise ValueError(f"No pending RFQ found for {client_rfq_id}")
        try:
            return await asyncio.wait_for(self.pending_rfqs[client_rfq_id], timeout=timeout)
        except asyncio.TimeoutError:
            self.pending_rfqs.pop(client_rfq_id,None)
            raise TimeoutError(f"Quote response timeout for {client_rfq_id}")
        finally:
            self.pending_rfqs.pop(client_rfq_id,None)

    async def _handle_quote_update_msg(self, data: Dict[str, Any]):
        #logger.info(f"Received quote update data: {json.dumps(data, indent=2)}")
        for qd in data.get("data",[]):
            client_rfq_id = qd.get("QuoteReqID")
            if not client_rfq_id:
                logger.error("Quote update data received without QuoteReqID")
                continue
            # Resolve pending future
            if client_rfq_id in self.pending_rfqs and qd['QuoteStatus']=='Open':
                self.pending_rfqs[client_rfq_id].set_result(qd)
                logger.info(f"Quote response received for {client_rfq_id}")


    async def execute_trade_request(self,request: TradeExecutionRequest)-> Tuple[Dict[str, Any], int]:
        PL_MAPPING = {"profit": 1, "loss": -1}
        try:
            # Basic checks
            # Submit new RFQ , await responce
            quote_request_id = str(uuid.uuid4())
            self.pending_rfqs[quote_request_id] = asyncio.Future()

            quote_request_resp = self._rfq_client.quote_request(
                    request.symbol,
                    request.currency,
                    request.order_qty,
                    request.order_side,
                    quote_request_id
            )
            latest_quote = await self._await_rfq_response(quote_request_id)
            if latest_quote['QuoteStatus']!='Open':
                logger.error(f"Failed to obtain quote for execution. Quote status: {latest_quote['QuoteStatus']}.")
                resp = TradeExecutionErrorResponse(
                    Timestamp=datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                    message=f"Failed to obtain quote for execution. Quote status: {latest_quote['QuoteStatus']}. Error details: {latest_quote['Text']}"
                )
                return resp.model_dump(),400    

            # Let us check profitability
            original_quote = request.original_quote_data.model_dump()
            if request.order_side.lower() == "buy":
                original_price = Decimal(original_quote["ask_price"])
                current_price = Decimal(latest_quote["OfferPx"])
            else:
                original_price = Decimal(original_quote["bid_price"])
                current_price = Decimal(latest_quote["BidPx"])
                
            price_diff = current_price - original_price
            pl_result = None
            if request.order_side.lower() == "buy":
                if price_diff > 0:
                    pl_result = 'loss'
                else:
                    pl_result = 'profit'
            else:
                if price_diff < 0:
                    pl_result = 'loss'
                else:
                    pl_result = 'profit'
            pl_result_amount = abs(price_diff) * Decimal(request.order_qty) * Decimal(PL_MAPPING[pl_result])
            pl_resp_info = ProfitLossInfo(
                result=pl_result,
                amount=str(pl_result_amount),
                original_quote_price=str(original_price),
                current_price=str(current_price)   
            )
            # TBD - Cancel execution if loss is too big


            # Submit order, await response and return result
            order_request_id = str(uuid.uuid4())
            self.pending_orders[order_request_id] = asyncio.Future()

            order_request_resp = self._orders_client.new_order_single(
                    symbol=request.symbol,
                    side=request.order_side,
                    order_qty=request.order_qty,
                    ord_type="RFQ",  # RFQ order type as required
                    time_in_force="FillOrKill",  # FillOrKill as required
                    quote_id=latest_quote["QuoteID"],
                    rfq_id=latest_quote["RFQID"],
                    cl_ord_id=order_request_id
            )
            execution_result = await self._await_trade_execution(order_request_id)
            if execution_result['OrdStatus']!='Filled':
                logger.error(f"Trade execution failed: {e}")
                resp = TradeExecutionErrorResponse(
                    Timestamp=datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                    message=f"Internal error during trade execution. Order status: {execution_result['OrdStatus']}. Error details: {execution_result['Text']}"
                )
                return resp.model_dump(),400              

            # Then, create an instance of TradeExecutionSuccessResponse
            resp = TradeExecutionSuccessResponse(
                Timestamp=execution_result["Timestamp"],
                Symbol=execution_result["Symbol"],
                OrderID=execution_result["OrderID"],
                ClOrdID=execution_result["ClOrdID"] ,
                SubmitTime=execution_result["SubmitTime"],
                ExecID=execution_result["ExecID"],
                Side=execution_result["Side"],
                OrderQty=execution_result["OrderQty"],
                Currency=execution_result["Currency"],
                AmountCurrency=execution_result["AmountCurrency"],
                RFQID=execution_result["RFQID"],
                profit_loss_info=pl_resp_info
            )
            return resp.model_dump(),200
        except Exception as e:
            logger.error(f"Trade execution error: {e}")
            resp = TradeExecutionErrorResponse(
                Timestamp=datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                message="Internal error during trade execution {e}"
            )
            return resp.model_dump(),500

# Singleton instance
_instance = None


def get_trade_execution_service() -> TradeExecutionService:
    global _instance
    if _instance is None:
        _instance = TradeExecutionService()
    else:
        if _instance._scrypt_client.session_id != _instance._scrypt_session_id:
            logger.info("Scrypt session changed. Reinitializing trade execution service.")
            _instance = TradeExecutionService()
    return _instance
