import json
import pandas as pd
from datetime import datetime
from decimal import Decimal
from typing import Any, Dict, List, Union

def ensure_json_serializable(obj: Any) -> Any:
    """
    Recursively convert an object to ensure it's JSON serializable.
    
    Args:
        obj: The object to convert
        
    Returns:
        A JSON serializable version of the object
    """
    if isinstance(obj, dict):
        return {k: ensure_json_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [ensure_json_serializable(item) for item in obj]
    elif isinstance(obj, (pd.Timestamp, datetime)):
        return obj.isoformat()
    elif isinstance(obj, Decimal):
        return float(obj)
    elif pd and hasattr(pd, 'NA') and obj is pd.NA:
        return None
    elif pd and isinstance(obj, pd.Series):
        return ensure_json_serializable(obj.to_dict())
    elif pd and isinstance(obj, pd.DataFrame):
        return ensure_json_serializable(obj.to_dict(orient='records'))
    else:
        return obj

def json_serialize(obj: Any) -> str:
    """
    Serialize an object to JSON string, ensuring all values are JSON serializable.
    
    Args:
        obj: The object to serialize
        
    Returns:
        JSON string representation of the object
    """
    return json.dumps(ensure_json_serializable(obj))