"""
Configuration for the WebSocket server.

This module loads configuration from environment variables or Hashicorp Vault.
"""

import os
import logging
import sys
from typing import Dict, Any, Optional

# Add the parent directory to the path to allow imports from app.hcvault
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from hcvault import get_vault_client, VaultConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Default configuration values
DEFAULT_HOST = "0.0.0.0"
DEFAULT_WS_PORT = 8766
DEFAULT_REST_PORT = 8081
DEFAULT_JWT_SECRET = "your-secret-key"  # Should be overridden in production
DEFAULT_VAULT_PATH = "kv/data/apps/trading-if"
DEFAULT_SCRYPT_VAULT_PATH = "kv/data/apps/trading-if"

# Environment variable names
ENV_HOST = "NG_WS_SERVER_HOST"
ENV_WS_PORT = "NG_WS_SERVER_PORT"
ENV_REST_PORT = "NG_REST_SERVER_PORT"
ENV_JWT_SECRET = "NG_WS_SERVER_JWT_SECRET"
ENV_VAULT_PATH = "NG_WS_SERVER_VAULT_PATH"
ENV_SCRYPT_VAULT_PATH = "NG_WS_SERVER_SCRYPT_VAULT_PATH"
ENV_VAULT_HOST = "VAULT_HOST"
ENV_VAULT_TOKEN = "VAULT_TOKEN"


class ServerConfig:
    """
    Configuration for the WebSocket server.
    """

    def __init__(self):
        """Initialize the server configuration."""
        # Server settings
        self.host = os.environ.get(ENV_HOST, DEFAULT_HOST)
        self.ws_port = int(os.environ.get(ENV_WS_PORT, DEFAULT_WS_PORT))
        self.rest_port = int(os.environ.get(ENV_REST_PORT, DEFAULT_REST_PORT))

        # JWT settings
        self.jwt_secret = os.environ.get(ENV_JWT_SECRET, DEFAULT_JWT_SECRET)

        # Vault settings
        self.vault_path = DEFAULT_VAULT_PATH
        self.scrypt_vault_path = os.environ.get(ENV_SCRYPT_VAULT_PATH, DEFAULT_SCRYPT_VAULT_PATH)

        # Spread settings (will be loaded from Vault)
        self.spreads = {
            "test-company": 40,
            "test-company-2": 50,
            "default": 20
        }

        # Other settings
        self.log_level = logging.INFO


# Create a singleton instance
_config_instance = None


def get_config() -> ServerConfig:
    """
    Get the ServerConfig singleton instance.

    Returns:
        The ServerConfig instance.
    """
    global _config_instance
    if _config_instance is None:
        _config_instance = ServerConfig()
    return _config_instance
